// pages/env-setup/env-setup.js
Page({
  data: {
    envId: 'danny-0g3qixz86747c5bb', // 当前配置的环境 ID
    testResult: '',
    isLoading: false,
    currentStatus: 'checking', // checking, success, error
    currentError: '',
    hasConfigured: true // 标记是否已配置
  },

  onLoad() {
    // 页面加载时检测当前状态
    this.checkCurrentStatus();
  },

  // 检测当前环境状态
  async checkCurrentStatus() {
    this.setData({ isLoading: true });

    try {
      // 测试当前配置的云函数调用
      const result = await wx.cloud.callFunction({
        name: 'getOpenId'
      });

      // 如果成功，说明环境配置正确
      this.setData({
        currentStatus: 'success',
        testResult: `✅ 当前环境配置正确！\nOpenID: ${result.result.openid}`,
        isLoading: false,
        hasConfigured: true
      });

    } catch (error) {
      console.error('当前环境检测失败:', error);

      // 如果失败，显示具体错误
      this.setData({
        currentStatus: 'error',
        currentError: error.errMsg || error.message,
        testResult: `❌ 当前环境配置有问题：\n${error.errMsg || error.message}`,
        isLoading: false,
        hasConfigured: false
      });
    }
  },

  // 输入环境 ID
  onEnvIdInput(e) {
    this.setData({
      envId: e.detail.value
    });
  },

  // 测试环境 ID
  async testEnvId() {
    const { envId } = this.data;
    
    if (!envId) {
      wx.showToast({
        title: '请输入环境 ID',
        icon: 'none'
      });
      return;
    }

    this.setData({ isLoading: true, testResult: '' });

    try {
      // 重新初始化云开发
      wx.cloud.init({
        env: envId,
        traceUser: true,
      });

      // 测试云函数调用
      const result = await wx.cloud.callFunction({
        name: 'getOpenId'
      });

      this.setData({
        testResult: `✅ 环境配置成功！\nOpenID: ${result.result.openid}`,
        isLoading: false
      });

      // 保存环境 ID 到本地存储
      wx.setStorageSync('cloudbase_env_id', envId);

    } catch (error) {
      console.error('环境测试失败:', error);
      this.setData({
        testResult: `❌ 环境配置失败：\n${error.errMsg || error.message}`,
        isLoading: false
      });
    }
  },

  // 复制环境 ID
  copyEnvId() {
    const { envId } = this.data;
    if (!envId) {
      wx.showToast({
        title: '请先输入环境 ID',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: envId,
      success() {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 打开云开发控制台
  openCloudConsole() {
    wx.showModal({
      title: '获取环境 ID',
      content: '请在微信开发者工具中点击"云开发"按钮，或访问腾讯云控制台获取环境 ID',
      confirmText: '我知道了',
      showCancel: false
    });
  },

  // 应用配置
  applyConfig() {
    const { envId } = this.data;
    if (!envId) {
      wx.showToast({
        title: '请先测试环境 ID',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '配置说明',
      content: `请将环境 ID "${envId}" 配置到 miniprogram/app.js 文件中的 wx.cloud.init() 方法里`,
      confirmText: '我知道了',
      showCancel: false
    });
  }
});
