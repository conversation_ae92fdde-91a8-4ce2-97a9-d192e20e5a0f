// pages/exhibition/list/list.js
Page({
  data: {
    exhibitions: [],
    loading: true,
    hasMore: true,
    page: 1,
    limit: 10
  },

  onLoad() {
    this.loadExhibitions();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  onPullDownRefresh() {
    this.refreshData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMore();
    }
  },

  // 刷新数据
  async refreshData() {
    this.setData({
      exhibitions: [],
      page: 1,
      hasMore: true,
      loading: true
    });
    await this.loadExhibitions();
  },

  // 加载展览列表
  async loadExhibitions() {
    if (!this.data.hasMore) return;

    this.setData({ loading: true });

    try {
      const result = await wx.cloud.callFunction({
        name: 'content-management',
        data: {
          action: 'getExhibitions',
          page: this.data.page,
          limit: this.data.limit
        }
      });

      if (result.result.success) {
        const newExhibitions = result.result.data || [];
        const allExhibitions = this.data.page === 1 ? newExhibitions : [...this.data.exhibitions, ...newExhibitions];
        
        this.setData({
          exhibitions: allExhibitions,
          hasMore: newExhibitions.length === this.data.limit,
          loading: false
        });
      } else {
        throw new Error(result.result.error);
      }
    } catch (error) {
      console.error('加载展览列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载更多
  async loadMore() {
    this.setData({
      page: this.data.page + 1
    });
    await this.loadExhibitions();
  },

  // 展览卡片点击
  onExhibitionTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/exhibition/detail/detail?id=${id}`
    });
  },

  // 报名按钮点击
  onRegisterTap(e) {
    e.stopPropagation();
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/registration/form/form?exhibitionId=${id}`
    });
  },

  // 格式化日期
  formatDate(dateStr) {
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'active': '进行中',
      'upcoming': '即将开始',
      'ended': '已结束',
      'inactive': '暂停'
    };
    return statusMap[status] || '未知';
  },

  // 获取状态样式
  getStatusClass(status) {
    const classMap = {
      'active': 'status-active',
      'upcoming': 'status-upcoming',
      'ended': 'status-ended',
      'inactive': 'status-inactive'
    };
    return classMap[status] || '';
  }
});
