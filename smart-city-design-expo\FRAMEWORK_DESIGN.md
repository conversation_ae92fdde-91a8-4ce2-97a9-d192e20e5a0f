# 🏗️ 智慧城市原创设计展小程序框架设计

## 📱 小程序页面架构

### 主要页面结构
```
smart-city-design-expo/
├── miniprogram/                    # 小程序前端代码
│   ├── pages/                      # 页面目录
│   │   ├── index/                  # 首页
│   │   │   ├── index.js
│   │   │   ├── index.wxml
│   │   │   ├── index.wxss
│   │   │   └── index.json
│   │   ├── exhibition/             # 展览页面
│   │   │   ├── list/               # 展览列表
│   │   │   ├── detail/             # 展览详情
│   │   │   └── works/              # 作品展示
│   │   ├── registration/           # 报名页面
│   │   │   ├── form/               # 报名表单
│   │   │   ├── upload/             # 作品上传
│   │   │   └── status/             # 状态查询
│   │   ├── news/                   # 资讯页面
│   │   │   ├── list/               # 新闻列表
│   │   │   └── detail/             # 新闻详情
│   │   └── profile/                # 个人中心
│   │       ├── my-registration/    # 我的报名
│   │       ├── my-favorites/       # 我的收藏
│   │       └── settings/           # 设置中心
│   ├── components/                 # 公共组件
│   │   ├── work-card/              # 作品卡片
│   │   ├── news-item/              # 新闻条目
│   │   ├── upload-panel/           # 上传面板
│   │   └── status-badge/           # 状态标签
│   ├── utils/                      # 工具函数
│   │   ├── api.js                  # API 接口
│   │   ├── util.js                 # 通用工具
│   │   └── constants.js            # 常量定义
│   ├── images/                     # 图片资源
│   ├── app.js                      # 小程序入口
│   ├── app.json                    # 小程序配置
│   └── app.wxss                    # 全局样式
├── cloudfunctions/                 # 云函数目录
│   ├── user-management/            # 用户管理
│   ├── content-management/         # 内容管理
│   ├── registration-system/        # 报名系统
│   └── notification-service/       # 通知服务
├── database/                       # 数据库设计
│   └── schema.json                 # 数据表结构
├── project.config.json             # 项目配置
├── cloudbaserc.json               # 云开发配置
└── README.md                       # 项目说明
```

## 🗄️ 数据库设计

### 核心数据表

#### 1. 用户表 (users)
```json
{
  "_id": "用户ID",
  "openid": "微信OpenID",
  "nickName": "用户昵称",
  "avatarUrl": "头像URL",
  "phone": "手机号",
  "email": "邮箱",
  "realName": "真实姓名",
  "organization": "所属机构",
  "profession": "职业",
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

#### 2. 展览信息表 (exhibitions)
```json
{
  "_id": "展览ID",
  "title": "展览标题",
  "description": "展览描述",
  "theme": "展览主题",
  "coverImage": "封面图片",
  "startDate": "开始时间",
  "endDate": "结束时间",
  "location": "展览地点",
  "status": "状态(active/inactive)",
  "viewCount": "浏览次数",
  "createTime": "创建时间"
}
```

#### 3. 作品信息表 (works)
```json
{
  "_id": "作品ID",
  "title": "作品标题",
  "description": "作品描述",
  "category": "作品分类",
  "tags": ["标签数组"],
  "images": ["图片URL数组"],
  "files": ["文件URL数组"],
  "authorId": "作者ID",
  "authorName": "作者姓名",
  "exhibitionId": "所属展览ID",
  "likeCount": "点赞数",
  "viewCount": "浏览数",
  "status": "状态(draft/published/rejected)",
  "createTime": "创建时间"
}
```

#### 4. 报名记录表 (registrations)
```json
{
  "_id": "报名ID",
  "userId": "用户ID",
  "exhibitionId": "展览ID",
  "workId": "作品ID",
  "category": "参赛类别",
  "contactInfo": {
    "name": "联系人姓名",
    "phone": "联系电话",
    "email": "联系邮箱"
  },
  "status": "状态(pending/approved/rejected)",
  "submitTime": "提交时间",
  "reviewTime": "审核时间",
  "reviewComment": "审核意见"
}
```

#### 5. 新闻资讯表 (news)
```json
{
  "_id": "新闻ID",
  "title": "新闻标题",
  "content": "新闻内容",
  "summary": "新闻摘要",
  "coverImage": "封面图片",
  "category": "新闻分类",
  "tags": ["标签数组"],
  "author": "作者",
  "publishTime": "发布时间",
  "viewCount": "浏览次数",
  "status": "状态(published/draft)"
}
```

## ☁️ 云函数架构

### 1. 用户管理 (user-management)
- **getUserInfo**: 获取用户信息
- **updateUserProfile**: 更新用户资料
- **getUserRegistrations**: 获取用户报名记录

### 2. 内容管理 (content-management)
- **getExhibitions**: 获取展览列表
- **getExhibitionDetail**: 获取展览详情
- **getWorks**: 获取作品列表
- **getWorkDetail**: 获取作品详情
- **getNews**: 获取新闻列表
- **getNewsDetail**: 获取新闻详情

### 3. 报名系统 (registration-system)
- **submitRegistration**: 提交报名
- **uploadWork**: 上传作品
- **getRegistrationStatus**: 查询报名状态
- **updateRegistration**: 更新报名信息

### 4. 互动功能 (interaction-service)
- **likeWork**: 点赞作品
- **favoriteWork**: 收藏作品
- **addComment**: 添加评论
- **shareWork**: 分享作品

## 🎨 UI/UX 设计原则

### 设计风格
- **现代简约**: 采用简洁的设计语言
- **科技感**: 体现智慧城市的科技特色
- **易用性**: 注重用户体验和操作便捷性

### 色彩方案
- **主色调**: 科技蓝 (#1890ff)
- **辅助色**: 智慧绿 (#52c41a)
- **强调色**: 创新橙 (#fa8c16)
- **中性色**: 灰色系 (#666, #999, #ccc)

### 组件规范
- **卡片式布局**: 内容以卡片形式展示
- **响应式设计**: 适配不同屏幕尺寸
- **统一间距**: 使用 8px 栅格系统
- **图标系统**: 使用一致的图标风格

## 🔧 技术特性

### CloudBase MCP 集成
- **自动化部署**: 使用 MCP 工具自动部署云函数
- **数据库管理**: 通过 MCP 管理数据库结构
- **文件上传**: 利用云存储处理作品文件
- **实时通知**: 集成消息推送功能

### 性能优化
- **图片懒加载**: 优化图片加载性能
- **分页加载**: 大数据量分页处理
- **缓存策略**: 合理使用本地缓存
- **代码分包**: 按需加载页面代码

## 📊 功能模块详解

### 首页模块
- 轮播图展示重要信息
- 快速入口导航
- 热门作品推荐
- 最新资讯展示

### 展览模块
- 展览主题介绍
- 作品分类浏览
- 搜索筛选功能
- 作品详情展示

### 报名模块
- 在线报名表单
- 作品文件上传
- 报名状态跟踪
- 审核结果通知

### 个人中心
- 用户信息管理
- 报名记录查看
- 收藏作品管理
- 消息通知中心

这个框架设计充分利用了 CloudBase 的能力，同时考虑了小程序的特点和用户体验。
