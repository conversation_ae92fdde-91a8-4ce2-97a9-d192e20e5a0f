<!--pages/init-data/init-data.wxml-->
<view class="container">
  <view class="init-header">
    <view class="init-icon">🚀</view>
    <text class="init-title">智慧城市设计展</text>
    <text class="init-subtitle">数据初始化工具</text>
  </view>

  <!-- 未初始化状态 -->
  <view class="init-content" wx:if="{{!initialized}}">
    <view class="init-description">
      <text class="desc-title">📋 初始化说明</text>
      <text class="desc-text">首次使用需要初始化模拟数据，包括：</text>
      <view class="desc-list">
        <text class="desc-item">• 轮播图数据</text>
        <text class="desc-item">• 展览信息</text>
        <text class="desc-item">• 作品展示</text>
        <text class="desc-item">• 新闻资讯</text>
      </view>
    </view>

    <view class="init-actions">
      <button class="btn btn-primary btn-large btn-block" 
              bindtap="initMockData" 
              loading="{{loading}}"
              disabled="{{loading}}">
        {{loading ? '初始化中...' : '🎯 开始初始化'}}
      </button>
    </view>
  </view>

  <!-- 已初始化状态 -->
  <view class="init-content" wx:else>
    <view class="success-info">
      <view class="success-icon">✅</view>
      <text class="success-title">数据初始化完成</text>
      <text class="success-desc">模拟数据已成功加载，您可以开始体验小程序功能了！</text>
    </view>

    <view class="feature-list">
      <text class="feature-title">🎉 可用功能</text>
      <view class="feature-items">
        <view class="feature-item">
          <text class="feature-icon">🏠</text>
          <text class="feature-text">首页展示</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🏛️</text>
          <text class="feature-text">展览浏览</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">📝</text>
          <text class="feature-text">在线报名</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">📰</text>
          <text class="feature-text">资讯阅读</text>
        </view>
      </view>
    </view>

    <view class="init-actions">
      <button class="btn btn-primary btn-large btn-block" bindtap="goToHome">
        🏠 进入首页
      </button>
      <button class="btn btn-secondary btn-large btn-block mt-20" bindtap="reinitData">
        🔄 重新初始化
      </button>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="init-footer">
    <text class="footer-text">智慧城市原创设计展小程序</text>
    <text class="footer-version">v1.0.0</text>
  </view>
</view>
