# 🚀 MCP 快速启动指南

## 📋 概述

本项目已配置完整的 MCP (Model Context Protocol) 服务，支持云开发功能的快速调用。

## 🎯 快速启动方法

### 方法 1：VS Code 任务（推荐）

1. **打开命令面板**：`Ctrl+Shift+P`
2. **运行任务**：输入 `Tasks: Run Task`
3. **选择任务**：
   - `🎯 一键启动所有服务` - 完整启动流程
   - `🚀 启动 CloudBase MCP` - 仅启动 MCP 服务
   - `🔍 检查 MCP 状态` - 检查 MCP 状态
   - `☁️ 检查云开发状态` - 检查云开发状态

### 方法 2：npm 脚本

```bash
# 检查并启动所有服务
npm run dev

# 仅启动 MCP 服务
npm run start:mcp

# 检查 MCP 状态
npm run check:mcp

# 检查云开发状态
npm run start:cloudbase

# 检查所有状态
npm run check:all
```

### 方法 3：直接命令

```bash
# 启动 CloudBase MCP
node start-mcp.js

# 检查 MCP 状态
node mcp-status.js

# 检查云开发状态
node cloudbase-startup.js
```

### 方法 4：VS Code 终端配置

1. **打开新终端**：`Ctrl+Shift+\``
2. **选择终端类型**：`CloudBase MCP`
3. **按照提示运行命令**

## 🔧 MCP 服务配置

### 当前配置的 MCP 服务

| 服务名称 | 命令 | 功能 |
|---------|------|------|
| CloudBase MCP | `npx @cloudbase/cloudbase-mcp@latest` | 云开发功能调用 |

### 可用的 MCP 工具

- `listFunctions` - 查询云函数列表
- `createFunction` - 创建/部署云函数
- `updateFunctionCode` - 更新云函数代码
- `uploadFiles` - 上传静态文件到云存储
- `getEnvInfo` - 获取云开发环境信息
- `getWebsiteConfig` - 获取静态网站配置
- `searchKnowledgeBase` - 搜索云开发知识库

## 📱 项目信息

- **环境 ID**: `danny-0g3qixz86747c5bb`
- **小程序 AppID**: `wxde0a110374e42908`
- **云函数**: `getOpenId` (已部署)

## 🎉 启动成功标志

### MCP 服务启动成功
```
✅ CloudBase MCP 已启动 (PID: xxxx)
📡 MCP 服务现在正在后台运行
```

### 云开发环境正常
```
✅ 环境配置正确
✅ 云函数已部署
✅ 项目结构完整
```

## 🔍 故障排除

### 问题 1：MCP 服务启动失败
```bash
# 检查 Node.js 版本
node --version

# 检查 npm 版本
npm --version

# 重新安装 CloudBase MCP
npm install -g @cloudbase/cloudbase-mcp@latest
```

### 问题 2：云开发连接失败
```bash
# 检查登录状态
tcb auth:list

# 重新登录
tcb login

# 检查环境列表
tcb env:list
```

### 问题 3：VS Code 任务不可用
1. 确保 `.vscode/tasks.json` 文件存在
2. 重新加载 VS Code 窗口：`Ctrl+Shift+P` → `Developer: Reload Window`

## 💡 使用建议

### 开发流程
1. **打开项目** → **运行 `npm run dev`** → **启动微信开发者工具**
2. **修改代码** → **使用 MCP 工具部署** → **测试功能**

### MCP 工具调用示例
在支持 MCP 的 AI 工具中，您可以这样请求：
- "请查询当前的云函数列表"
- "请部署 getOpenId 云函数"
- "请获取云开发环境信息"
- "请上传文件到云存储"

## 🔗 相关文件

- `.vscode/mcp.json` - MCP 服务配置
- `.vscode/tasks.json` - VS Code 任务配置
- `.vscode/settings.json` - VS Code 设置
- `package.json` - npm 脚本配置
- `cloudbaserc.json` - 云开发配置

## 📚 更多信息

- [CloudBase MCP 文档](https://github.com/TencentCloudBase/cloudbase-mcp)
- [微信小程序云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html)
- [MCP 协议规范](https://modelcontextprotocol.io/)

---

🎯 **下次打开项目时，只需运行 `npm run dev` 即可一键启动所有服务！**
