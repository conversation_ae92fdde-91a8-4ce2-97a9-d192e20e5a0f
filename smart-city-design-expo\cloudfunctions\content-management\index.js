// 内容管理云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { action } = event;

  try {
    switch (action) {
      case 'getBanners':
        return await getBanners();
      case 'getHotWorks':
        return await getHotWorks(event.limit || 6);
      case 'getLatestNews':
        return await getLatestNews(event.limit || 3);
      case 'getActiveExhibitions':
        return await getActiveExhibitions(event.limit || 3);
      case 'getExhibitions':
        return await getExhibitions(event.page || 1, event.limit || 10);
      case 'getExhibitionDetail':
        return await getExhibitionDetail(event.id);
      case 'getWorks':
        return await getWorks(event.page || 1, event.limit || 10, event.category);
      case 'getWorkDetail':
        return await getWorkDetail(event.id);
      case 'getNews':
        return await getNews(event.page || 1, event.limit || 10);
      case 'getNewsDetail':
        return await getNewsDetail(event.id);
      case 'initMockData':
        return await initMockData();
      default:
        return { success: false, error: '未知操作' };
    }
  } catch (error) {
    console.error('内容管理云函数错误:', error);
    return { success: false, error: error.message };
  }
};

// 获取轮播图
async function getBanners() {
  try {
    const result = await db.collection('banners')
      .where({
        status: 'active'
      })
      .orderBy('order', 'asc')
      .get();

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取轮播图失败: ' + error.message);
  }
}

// 获取热门作品
async function getHotWorks(limit) {
  try {
    const result = await db.collection('works')
      .where({
        status: 'published'
      })
      .orderBy('likeCount', 'desc')
      .limit(limit)
      .get();

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取热门作品失败: ' + error.message);
  }
}

// 获取最新资讯
async function getLatestNews(limit) {
  try {
    const result = await db.collection('news')
      .where({
        status: 'published'
      })
      .orderBy('publishTime', 'desc')
      .limit(limit)
      .get();

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取最新资讯失败: ' + error.message);
  }
}

// 获取活跃展览
async function getActiveExhibitions(limit) {
  try {
    const result = await db.collection('exhibitions')
      .where({
        status: 'active'
      })
      .orderBy('startDate', 'desc')
      .limit(limit)
      .get();

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取活跃展览失败: ' + error.message);
  }
}

// 获取展览列表
async function getExhibitions(page, limit) {
  try {
    const skip = (page - 1) * limit;
    const result = await db.collection('exhibitions')
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get();

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取展览列表失败: ' + error.message);
  }
}

// 获取展览详情
async function getExhibitionDetail(id) {
  try {
    const result = await db.collection('exhibitions').doc(id).get();
    
    // 增加浏览次数
    await db.collection('exhibitions').doc(id).update({
      data: {
        viewCount: db.command.inc(1)
      }
    });

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取展览详情失败: ' + error.message);
  }
}

// 获取作品列表
async function getWorks(page, limit, category) {
  try {
    const skip = (page - 1) * limit;
    let query = db.collection('works').where({
      status: 'published'
    });

    if (category) {
      query = query.where({
        category: category
      });
    }

    const result = await query
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get();

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取作品列表失败: ' + error.message);
  }
}

// 获取作品详情
async function getWorkDetail(id) {
  try {
    const result = await db.collection('works').doc(id).get();
    
    // 增加浏览次数
    await db.collection('works').doc(id).update({
      data: {
        viewCount: db.command.inc(1)
      }
    });

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取作品详情失败: ' + error.message);
  }
}

// 获取新闻列表
async function getNews(page, limit) {
  try {
    const skip = (page - 1) * limit;
    const result = await db.collection('news')
      .where({
        status: 'published'
      })
      .orderBy('publishTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get();

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取新闻列表失败: ' + error.message);
  }
}

// 获取新闻详情
async function getNewsDetail(id) {
  try {
    const result = await db.collection('news').doc(id).get();
    
    // 增加浏览次数
    await db.collection('news').doc(id).update({
      data: {
        viewCount: db.command.inc(1)
      }
    });

    return { success: true, data: result.data };
  } catch (error) {
    throw new Error('获取新闻详情失败: ' + error.message);
  }
}

// 初始化模拟数据
async function initMockData() {
  try {
    // 清空现有数据
    await Promise.all([
      db.collection('banners').get().then(res => {
        const batch = db.batch();
        res.data.forEach(doc => batch.remove(db.collection('banners').doc(doc._id)));
        return batch.commit();
      }),
      db.collection('exhibitions').get().then(res => {
        const batch = db.batch();
        res.data.forEach(doc => batch.remove(db.collection('exhibitions').doc(doc._id)));
        return batch.commit();
      }),
      db.collection('works').get().then(res => {
        const batch = db.batch();
        res.data.forEach(doc => batch.remove(db.collection('works').doc(doc._id)));
        return batch.commit();
      }),
      db.collection('news').get().then(res => {
        const batch = db.batch();
        res.data.forEach(doc => batch.remove(db.collection('news').doc(doc._id)));
        return batch.commit();
      })
    ]);

    // 插入模拟数据
    await insertMockData();

    return { success: true, message: '模拟数据初始化完成' };
  } catch (error) {
    throw new Error('初始化模拟数据失败: ' + error.message);
  }
}

// 插入模拟数据
async function insertMockData() {
  const now = new Date();
  
  // 轮播图数据
  const banners = [
    {
      title: '2024智慧城市原创设计展',
      image: 'https://picsum.photos/750/300?random=1',
      linkType: 'exhibition',
      linkId: 'expo2024',
      order: 1,
      status: 'active',
      startTime: now,
      endTime: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000),
      createTime: now
    },
    {
      title: '智慧交通设计大赛',
      image: 'https://picsum.photos/750/300?random=2',
      linkType: 'exhibition',
      linkId: 'traffic2024',
      order: 2,
      status: 'active',
      startTime: now,
      endTime: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000),
      createTime: now
    }
  ];

  // 展览数据
  const exhibitions = [
    {
      _id: 'expo2024',
      title: '2024智慧城市原创设计展',
      description: '展示最新的智慧城市设计理念和创新方案，汇聚全球优秀设计师的智慧结晶。',
      theme: '智慧未来，设计先行',
      coverImage: 'https://picsum.photos/400/300?random=10',
      images: [
        'https://picsum.photos/800/600?random=11',
        'https://picsum.photos/800/600?random=12'
      ],
      startDate: now,
      endDate: new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000),
      location: '深圳会展中心',
      status: 'active',
      category: '综合展览',
      tags: ['智慧城市', '原创设计', '科技创新'],
      viewCount: 1250,
      likeCount: 89,
      registrationCount: 156,
      maxRegistration: 500,
      registrationStartDate: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000),
      registrationEndDate: new Date(now.getTime() + 20 * 24 * 60 * 60 * 1000),
      createTime: now,
      updateTime: now
    }
  ];

  // 作品数据
  const works = [
    {
      title: '智慧公交站台设计',
      description: '集成太阳能充电、实时信息显示、环境监测等功能的智能公交站台设计方案。',
      category: '交通设施',
      tags: ['公交', '智能', '环保'],
      images: [
        'https://picsum.photos/400/300?random=20',
        'https://picsum.photos/400/300?random=21'
      ],
      authorId: 'mock_user_1',
      authorName: '张设计师',
      authorOrganization: '深圳设计院',
      exhibitionId: 'expo2024',
      likeCount: 45,
      viewCount: 320,
      commentCount: 12,
      favoriteCount: 28,
      status: 'published',
      featured: true,
      createTime: now,
      updateTime: now,
      publishTime: now
    },
    {
      title: '智慧社区管理系统',
      description: '基于物联网技术的智慧社区综合管理平台，提供安防、物业、生活服务一体化解决方案。',
      category: '社区服务',
      tags: ['社区', '物联网', '管理系统'],
      images: [
        'https://picsum.photos/400/300?random=22',
        'https://picsum.photos/400/300?random=23'
      ],
      authorId: 'mock_user_2',
      authorName: '李工程师',
      authorOrganization: '智慧科技公司',
      exhibitionId: 'expo2024',
      likeCount: 67,
      viewCount: 480,
      commentCount: 18,
      favoriteCount: 35,
      status: 'published',
      featured: true,
      createTime: now,
      updateTime: now,
      publishTime: now
    }
  ];

  // 新闻数据
  const news = [
    {
      title: '2024智慧城市设计展即将开幕',
      content: '由深圳市政府主办的2024智慧城市原创设计展将于本月底在深圳会展中心隆重开幕。本次展览汇聚了来自全球的优秀设计师和创新团队，展示最前沿的智慧城市设计理念和解决方案。',
      summary: '2024智慧城市原创设计展即将在深圳会展中心开幕，展示全球最新智慧城市设计理念。',
      coverImage: 'https://picsum.photos/400/300?random=30',
      category: '展览资讯',
      tags: ['展览', '智慧城市', '设计'],
      author: '新闻编辑部',
      source: '官方发布',
      publishTime: now,
      viewCount: 890,
      likeCount: 56,
      commentCount: 23,
      status: 'published',
      featured: true,
      createTime: now,
      updateTime: now
    },
    {
      title: '智慧城市建设的新趋势',
      content: '随着5G、人工智能、物联网等技术的快速发展，智慧城市建设正迎来新的发展机遇。本文深入分析了当前智慧城市建设的主要趋势和发展方向。',
      summary: '分析5G、AI、物联网等技术推动下的智慧城市建设新趋势和发展方向。',
      coverImage: 'https://picsum.photos/400/300?random=31',
      category: '行业分析',
      tags: ['智慧城市', '5G', '人工智能'],
      author: '技术专家',
      source: '行业报告',
      publishTime: new Date(now.getTime() - 24 * 60 * 60 * 1000),
      viewCount: 1200,
      likeCount: 78,
      commentCount: 34,
      status: 'published',
      featured: false,
      createTime: new Date(now.getTime() - 24 * 60 * 60 * 1000),
      updateTime: new Date(now.getTime() - 24 * 60 * 60 * 1000)
    }
  ];

  // 批量插入数据
  await Promise.all([
    ...banners.map(banner => db.collection('banners').add({ data: banner })),
    ...exhibitions.map(exhibition => db.collection('exhibitions').add({ data: exhibition })),
    ...works.map(work => db.collection('works').add({ data: work })),
    ...news.map(newsItem => db.collection('news').add({ data: newsItem }))
  ]);
}
