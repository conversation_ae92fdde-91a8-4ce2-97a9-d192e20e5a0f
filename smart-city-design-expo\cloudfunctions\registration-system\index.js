// 报名系统云函数
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { action } = event;
  const wxContext = cloud.getWXContext();

  try {
    switch (action) {
      case 'submitRegistration':
        return await submitRegistration(wxContext.OPENID, event.registrationData);
      case 'getRegistrationStatus':
        return await getRegistrationStatus(wxContext.OPENID, event.exhibitionId);
      case 'updateRegistration':
        return await updateRegistration(wxContext.OPENID, event.registrationId, event.updateData);
      case 'uploadWork':
        return await uploadWork(wxContext.OPENID, event.workData);
      default:
        return { success: false, error: '未知操作' };
    }
  } catch (error) {
    console.error('报名系统云函数错误:', error);
    return { success: false, error: error.message };
  }
};

// 提交报名
async function submitRegistration(openid, registrationData) {
  try {
    // 获取用户信息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();

    if (userResult.data.length === 0) {
      throw new Error('用户不存在，请先完善个人信息');
    }

    const user = userResult.data[0];

    // 检查是否已经报名
    const existingResult = await db.collection('registrations').where({
      userId: user._id,
      exhibitionId: registrationData.exhibitionId
    }).get();

    if (existingResult.data.length > 0) {
      throw new Error('您已经报名过此展览');
    }

    // 创建报名记录
    const registration = {
      userId: user._id,
      exhibitionId: registrationData.exhibitionId,
      workId: registrationData.workId || '',
      category: registrationData.category,
      contactInfo: {
        name: registrationData.contactInfo.name || user.realName,
        phone: registrationData.contactInfo.phone || user.phone,
        email: registrationData.contactInfo.email || user.email,
        organization: registrationData.contactInfo.organization || user.organization,
        position: registrationData.contactInfo.position || user.profession
      },
      workInfo: registrationData.workInfo || {},
      status: 'pending',
      submitTime: new Date(),
      reviewTime: null,
      reviewComment: '',
      reviewerId: ''
    };

    const result = await db.collection('registrations').add({
      data: registration
    });

    // 更新展览报名人数
    await db.collection('exhibitions').doc(registrationData.exhibitionId).update({
      data: {
        registrationCount: db.command.inc(1)
      }
    });

    return { 
      success: true, 
      data: { 
        registrationId: result._id,
        message: '报名提交成功，请等待审核'
      }
    };
  } catch (error) {
    throw new Error('提交报名失败: ' + error.message);
  }
}

// 获取报名状态
async function getRegistrationStatus(openid, exhibitionId) {
  try {
    // 获取用户信息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();

    if (userResult.data.length === 0) {
      return { success: true, data: null };
    }

    const user = userResult.data[0];

    // 获取报名记录
    const result = await db.collection('registrations').where({
      userId: user._id,
      exhibitionId: exhibitionId
    }).get();

    if (result.data.length === 0) {
      return { success: true, data: null };
    }

    return { success: true, data: result.data[0] };
  } catch (error) {
    throw new Error('获取报名状态失败: ' + error.message);
  }
}

// 更新报名信息
async function updateRegistration(openid, registrationId, updateData) {
  try {
    // 获取用户信息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();

    if (userResult.data.length === 0) {
      throw new Error('用户不存在');
    }

    const user = userResult.data[0];

    // 检查报名记录是否属于当前用户
    const registrationResult = await db.collection('registrations').doc(registrationId).get();
    
    if (!registrationResult.data || registrationResult.data.userId !== user._id) {
      throw new Error('无权限修改此报名记录');
    }

    // 只允许修改待审核状态的报名
    if (registrationResult.data.status !== 'pending') {
      throw new Error('只能修改待审核状态的报名记录');
    }

    // 更新报名信息
    const result = await db.collection('registrations').doc(registrationId).update({
      data: {
        ...updateData,
        updateTime: new Date()
      }
    });

    return { success: true, data: result };
  } catch (error) {
    throw new Error('更新报名信息失败: ' + error.message);
  }
}

// 上传作品
async function uploadWork(openid, workData) {
  try {
    // 获取用户信息
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();

    if (userResult.data.length === 0) {
      throw new Error('用户不存在');
    }

    const user = userResult.data[0];

    // 创建作品记录
    const work = {
      title: workData.title,
      description: workData.description,
      category: workData.category,
      tags: workData.tags || [],
      images: workData.images || [],
      files: workData.files || [],
      authorId: user._id,
      authorName: user.realName || user.nickName,
      authorOrganization: user.organization || '',
      exhibitionId: workData.exhibitionId,
      likeCount: 0,
      viewCount: 0,
      commentCount: 0,
      favoriteCount: 0,
      status: 'reviewing',
      featured: false,
      createTime: new Date(),
      updateTime: new Date(),
      publishTime: null
    };

    const result = await db.collection('works').add({
      data: work
    });

    return { 
      success: true, 
      data: { 
        workId: result._id,
        message: '作品上传成功，请等待审核'
      }
    };
  } catch (error) {
    throw new Error('上传作品失败: ' + error.message);
  }
}
