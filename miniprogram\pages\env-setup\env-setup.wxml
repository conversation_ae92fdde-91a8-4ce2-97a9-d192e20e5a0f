<!--pages/env-setup/env-setup.wxml-->
<view class="container">
  <view class="header">
    <text class="title">🔧 云开发环境配置</text>
    <text class="subtitle">解决环境 ID 配置问题</text>
  </view>

  <!-- 当前状态检测 -->
  <view class="section">
    <view class="section-title">📋 当前状态</view>

    <!-- 检测中 -->
    <view class="checking-box" wx:if="{{currentStatus === 'checking'}}">
      <text class="checking-text">🔄 正在检测当前环境配置...</text>
    </view>

    <!-- 配置成功 -->
    <view class="success-box" wx:if="{{currentStatus === 'success'}}">
      <text class="success-text">✅ 环境配置正确</text>
      <text class="success-desc">当前环境 ID: {{envId}}</text>
      <text class="success-desc">云函数调用正常，配置无需修改</text>
    </view>

    <!-- 配置错误 -->
    <view class="error-box" wx:if="{{currentStatus === 'error'}}">
      <text class="error-text">❌ 环境配置问题</text>
      <text class="error-desc">{{currentError}}</text>
    </view>
  </view>

  <!-- 环境信息显示 -->
  <view class="section" wx:if="{{currentStatus === 'success'}}">
    <view class="section-title">✅ 当前环境信息</view>
    <view class="env-info">
      <view class="env-item">
        <text class="env-label">环境 ID：</text>
        <text class="env-value">{{envId}}</text>
      </view>
      <view class="env-item">
        <text class="env-label">状态：</text>
        <text class="env-value success">正常运行</text>
      </view>
      <view class="env-item">
        <text class="env-label">云函数：</text>
        <text class="env-value success">已部署</text>
      </view>
    </view>
  </view>

  <!-- 获取环境 ID 指导（仅在有问题时显示） -->
  <view class="section" wx:if="{{currentStatus === 'error'}}">
    <view class="section-title">🔍 获取环境 ID</view>
    <view class="steps">
      <view class="step">
        <text class="step-number">1</text>
        <text class="step-text">在微信开发者工具中点击"云开发"按钮</text>
      </view>
      <view class="step">
        <text class="step-number">2</text>
        <text class="step-text">在云开发控制台中查看环境 ID</text>
      </view>
      <view class="step">
        <text class="step-number">3</text>
        <text class="step-text">复制环境 ID（格式如：cloud1-xxx-xxx）</text>
      </view>
    </view>
    <button class="btn-secondary" bindtap="openCloudConsole">📖 查看详细说明</button>
  </view>

  <!-- 测试环境 ID（仅在有问题时显示） -->
  <view class="section" wx:if="{{currentStatus === 'error'}}">
    <view class="section-title">🧪 测试环境 ID</view>
    <view class="input-group">
      <text class="input-label">环境 ID：</text>
      <input
        class="input"
        placeholder="请输入您的云开发环境 ID"
        value="{{envId}}"
        bindinput="onEnvIdInput"
      />
      <button class="btn-copy" bindtap="copyEnvId">复制</button>
    </view>

    <button
      class="btn-primary"
      bindtap="testEnvId"
      loading="{{isLoading}}"
      disabled="{{isLoading}}"
    >
      {{isLoading ? '测试中...' : '🧪 测试环境 ID'}}
    </button>

    <view class="result-box" wx:if="{{testResult}}">
      <text class="result-text">{{testResult}}</text>
    </view>
  </view>

  <!-- 重新检测按钮（环境正常时显示） -->
  <view class="section" wx:if="{{currentStatus === 'success'}}">
    <view class="section-title">🔄 重新检测</view>
    <text class="recheck-desc">如果您修改了环境配置，可以重新检测状态</text>
    <button
      class="btn-primary"
      bindtap="checkCurrentStatus"
      loading="{{isLoading}}"
      disabled="{{isLoading}}"
    >
      {{isLoading ? '检测中...' : '🔄 重新检测环境'}}
    </button>
  </view>

  <view class="section" wx:if="{{testResult && testResult.indexOf('✅') === 0}}">
    <view class="section-title">✅ 应用配置</view>
    <text class="config-desc">测试成功！请将此环境 ID 配置到项目中：</text>
    <view class="code-box">
      <text class="code">wx.cloud.init({\n  env: '{{envId}}',\n  traceUser: true,\n});</text>
    </view>
    <button class="btn-success" bindtap="applyConfig">📝 查看配置说明</button>
  </view>

  <view class="section">
    <view class="section-title">🆘 需要帮助？</view>
    <view class="help-links">
      <text class="help-text">• 微信开发者工具 → 云开发控制台</text>
      <text class="help-text">• 腾讯云控制台：console.cloud.tencent.com/tcb</text>
      <text class="help-text">• 如果没有环境，请先创建云开发环境</text>
    </view>
  </view>
</view>
