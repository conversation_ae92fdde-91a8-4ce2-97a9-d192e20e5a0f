#!/usr/bin/env node

/**
 * MCP 状态检查工具
 * 类似 Cursor 的 MCP 管理功能
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 MCP 状态检查工具');
console.log('=' .repeat(50));

// 1. 检查 MCP 配置文件
function checkMCPConfig() {
  console.log('\n📋 1. MCP 配置检查');
  console.log('-'.repeat(30));
  
  const mcpConfigPath = path.join(process.cwd(), '.vscode', 'mcp.json');
  
  if (fs.existsSync(mcpConfigPath)) {
    console.log('✅ MCP 配置文件存在:', mcpConfigPath);
    
    try {
      const config = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'));
      console.log('📄 配置内容:');
      console.log(JSON.stringify(config, null, 2));
      
      // 检查服务器配置
      if (config.servers) {
        console.log('\n🖥️  配置的 MCP 服务器:');
        Object.keys(config.servers).forEach(serverName => {
          const server = config.servers[serverName];
          console.log(`  - ${serverName}:`);
          console.log(`    命令: ${server.command}`);
          console.log(`    参数: ${server.args ? server.args.join(' ') : '无'}`);
        });
      }
    } catch (error) {
      console.log('❌ 配置文件解析失败:', error.message);
    }
  } else {
    console.log('❌ MCP 配置文件不存在');
    console.log('💡 建议创建 .vscode/mcp.json 文件');
  }
}

// 2. 检查 CloudBase MCP 包状态
function checkCloudBaseMCP() {
  console.log('\n📦 2. CloudBase MCP 包检查');
  console.log('-'.repeat(30));
  
  try {
    // 检查全局安装
    const globalResult = execSync('npm list -g @cloudbase/cloudbase-mcp', { 
      encoding: 'utf8', 
      timeout: 10000 
    });
    console.log('✅ 全局安装状态:');
    console.log(globalResult);
  } catch (error) {
    console.log('⚠️  全局包检查失败:', error.message);
  }
  
  try {
    // 检查本地安装
    const localResult = execSync('npm list @cloudbase/cloudbase-mcp', { 
      encoding: 'utf8', 
      timeout: 10000 
    });
    console.log('✅ 本地安装状态:');
    console.log(localResult);
  } catch (error) {
    console.log('⚠️  本地包未安装');
  }
}

// 3. 检查 MCP 工具可用性
function checkMCPTools() {
  console.log('\n🛠️  3. MCP 工具可用性检查');
  console.log('-'.repeat(30));
  
  const tools = [
    'listFunctions',
    'createFunction', 
    'updateFunctionCode',
    'uploadFiles',
    'getEnvInfo',
    'getWebsiteConfig',
    'searchKnowledgeBase'
  ];
  
  console.log('📋 理论上可用的 MCP 工具:');
  tools.forEach(tool => {
    console.log(`  ✅ ${tool} - 云开发操作工具`);
  });
  
  console.log('\n💡 注意: 这些工具需要通过支持 MCP 的 AI Agent 调用');
}

// 4. 检查环境信息
function checkEnvironment() {
  console.log('\n🌍 4. 环境信息检查');
  console.log('-'.repeat(30));
  
  console.log('📋 系统信息:');
  console.log(`  - Node.js 版本: ${process.version}`);
  console.log(`  - 操作系统: ${process.platform}`);
  console.log(`  - 当前目录: ${process.cwd()}`);
  
  // 检查云开发配置
  const cloudbaseConfig = path.join(process.cwd(), 'cloudbaserc.json');
  if (fs.existsSync(cloudbaseConfig)) {
    console.log('✅ CloudBase 配置文件存在');
    try {
      const config = JSON.parse(fs.readFileSync(cloudbaseConfig, 'utf8'));
      console.log(`  - 环境 ID: ${config.envId}`);
      console.log(`  - 版本: ${config.version}`);
    } catch (error) {
      console.log('⚠️  配置文件解析失败');
    }
  }
}

// 5. 提供建议
function provideSuggestions() {
  console.log('\n💡 5. 使用建议');
  console.log('-'.repeat(30));
  
  console.log('🎯 如何使用 MCP 功能:');
  console.log('  1. 确保在支持 MCP 的 AI 环境中 (如 Claude Desktop, Cursor 等)');
  console.log('  2. MCP 服务器会自动启动并提供工具');
  console.log('  3. 通过自然语言请求 AI 调用 MCP 工具');
  
  console.log('\n🔧 故障排除:');
  console.log('  - 如果 MCP 不工作，尝试重启 AI 应用');
  console.log('  - 检查网络连接和权限');
  console.log('  - 确保 CloudBase 账号已登录');
  
  console.log('\n📚 可用操作:');
  console.log('  - "部署云函数" - 使用 createFunction 工具');
  console.log('  - "查询云函数列表" - 使用 listFunctions 工具');
  console.log('  - "上传静态文件" - 使用 uploadFiles 工具');
  console.log('  - "获取环境信息" - 使用 getEnvInfo 工具');
}

// 主函数
function main() {
  try {
    checkMCPConfig();
    checkCloudBaseMCP();
    checkMCPTools();
    checkEnvironment();
    provideSuggestions();
    
    console.log('\n🎉 MCP 状态检查完成!');
    console.log('=' .repeat(50));
    
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error.message);
  }
}

// 运行检查
main();
