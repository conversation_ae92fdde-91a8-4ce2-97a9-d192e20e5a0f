{"version": "2.0.0", "tasks": [{"label": "🚀 启动 CloudBase MCP", "type": "shell", "command": "npx", "args": ["-y", "@cloudbase/cloudbase-mcp@latest"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "runOptions": {"runOn": "folderOpen"}, "problemMatcher": [], "detail": "启动 CloudBase MCP 服务器"}, {"label": "🔍 检查 MCP 状态", "type": "shell", "command": "node", "args": ["mcp-status.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "detail": "检查 MCP 服务状态和配置"}, {"label": "☁️ 检查云开发状态", "type": "shell", "command": "node", "args": ["cloudbase-startup.js"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "detail": "检查云开发环境和项目状态"}, {"label": "🎯 一键启动所有服务", "dependsOrder": "sequence", "dependsOn": ["🔍 检查 MCP 状态", "☁️ 检查云开发状态", "🚀 启动 CloudBase MCP"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "detail": "检查状态并启动所有 MCP 服务"}]}