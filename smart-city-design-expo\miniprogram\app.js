// app.js
App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'danny-0g3qixz86747c5bb', // 使用现有的云开发环境
        traceUser: true,
      });
    }

    // 获取用户信息
    this.globalData = {
      userInfo: null,
      openid: null
    };
  },

  onShow() {
    // 小程序显示时的逻辑
  },

  onHide() {
    // 小程序隐藏时的逻辑
  },

  onError(msg) {
    console.error('小程序错误:', msg);
  },

  // 全局数据
  globalData: {
    userInfo: null,
    openid: null
  },

  // 获取用户OpenID
  async getUserOpenId() {
    if (this.globalData.openid) {
      return this.globalData.openid;
    }

    try {
      const result = await wx.cloud.callFunction({
        name: 'getOpenId'
      });
      this.globalData.openid = result.result.openid;
      return this.globalData.openid;
    } catch (error) {
      console.error('获取OpenID失败:', error);
      return null;
    }
  },

  // 获取用户信息
  async getUserInfo() {
    if (this.globalData.userInfo) {
      return this.globalData.userInfo;
    }

    try {
      const openid = await this.getUserOpenId();
      if (!openid) return null;

      const result = await wx.cloud.callFunction({
        name: 'user-management',
        data: {
          action: 'getUserInfo',
          openid: openid
        }
      });

      this.globalData.userInfo = result.result.data;
      return this.globalData.userInfo;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }
});
