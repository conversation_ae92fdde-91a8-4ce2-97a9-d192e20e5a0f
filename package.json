{"name": "miniprogram-cloudbase-template", "version": "1.0.0", "description": "微信小程序 + 云开发模板项目", "main": "index.js", "scripts": {"start:mcp": "node start-mcp.js", "check:mcp": "node mcp-status.js", "start:cloudbase": "node cloudbase-startup.js", "check:all": "npm run check:mcp && npm run start:cloudbase", "dev": "npm run check:all && npm run start:mcp", "postinstall": "echo '🎉 项目依赖安装完成！运行 npm run dev 启动开发环境'"}, "keywords": ["miniprogram", "cloudbase", "mcp", "wechat"], "author": "", "license": "MIT", "devDependencies": {}, "dependencies": {}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": ""}, "bugs": {"url": ""}, "homepage": ""}