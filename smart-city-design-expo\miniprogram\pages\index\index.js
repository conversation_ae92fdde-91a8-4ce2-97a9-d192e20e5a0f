// pages/index/index.js
Page({
  data: {
    banners: [],
    hotWorks: [],
    latestNews: [],
    exhibitions: [],
    loading: true
  },

  onLoad() {
    this.loadPageData();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadPageData();
  },

  onPullDownRefresh() {
    this.loadPageData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载页面数据
  async loadPageData() {
    this.setData({ loading: true });
    
    try {
      await Promise.all([
        this.loadBanners(),
        this.loadHotWorks(),
        this.loadLatestNews(),
        this.loadExhibitions()
      ]);
    } catch (error) {
      console.error('加载页面数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载轮播图
  async loadBanners() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'content-management',
        data: {
          action: 'getBanners'
        }
      });
      
      this.setData({
        banners: result.result.data || []
      });
    } catch (error) {
      console.error('加载轮播图失败:', error);
    }
  },

  // 加载热门作品
  async loadHotWorks() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'content-management',
        data: {
          action: 'getHotWorks',
          limit: 6
        }
      });
      
      this.setData({
        hotWorks: result.result.data || []
      });
    } catch (error) {
      console.error('加载热门作品失败:', error);
    }
  },

  // 加载最新资讯
  async loadLatestNews() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'content-management',
        data: {
          action: 'getLatestNews',
          limit: 3
        }
      });
      
      this.setData({
        latestNews: result.result.data || []
      });
    } catch (error) {
      console.error('加载最新资讯失败:', error);
    }
  },

  // 加载展览信息
  async loadExhibitions() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'content-management',
        data: {
          action: 'getActiveExhibitions',
          limit: 3
        }
      });
      
      this.setData({
        exhibitions: result.result.data || []
      });
    } catch (error) {
      console.error('加载展览信息失败:', error);
    }
  },

  // 轮播图点击
  onBannerTap(e) {
    const { item } = e.currentTarget.dataset;
    if (item.linkType === 'exhibition') {
      wx.navigateTo({
        url: `/pages/exhibition/detail/detail?id=${item.linkId}`
      });
    } else if (item.linkType === 'news') {
      wx.navigateTo({
        url: `/pages/news/detail/detail?id=${item.linkId}`
      });
    }
  },

  // 快速入口点击
  onQuickEntryTap(e) {
    const { type } = e.currentTarget.dataset;
    
    switch (type) {
      case 'exhibition':
        wx.switchTab({
          url: '/pages/exhibition/list/list'
        });
        break;
      case 'registration':
        wx.switchTab({
          url: '/pages/registration/form/form'
        });
        break;
      case 'news':
        wx.switchTab({
          url: '/pages/news/list/list'
        });
        break;
      case 'profile':
        wx.switchTab({
          url: '/pages/profile/profile'
        });
        break;
    }
  },

  // 作品卡片点击
  onWorkTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/exhibition/works/works?id=${id}`
    });
  },

  // 新闻条目点击
  onNewsTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/news/detail/detail?id=${id}`
    });
  },

  // 展览卡片点击
  onExhibitionTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/exhibition/detail/detail?id=${id}`
    });
  },

  // 查看更多热门作品
  onMoreHotWorks() {
    wx.switchTab({
      url: '/pages/exhibition/list/list'
    });
  },

  // 查看更多资讯
  onMoreNews() {
    wx.switchTab({
      url: '/pages/news/list/list'
    });
  }
});
