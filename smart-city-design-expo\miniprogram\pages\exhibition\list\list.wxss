/* pages/exhibition/list/list.wxss */

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: #ffffff;
  margin-bottom: 30rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.page-subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

/* 展览列表 */
.exhibitions-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  padding-bottom: 40rpx;
}

.exhibition-card {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.exhibition-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

/* 展览封面 */
.exhibition-cover {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.exhibition-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.exhibition-card:active .exhibition-image {
  transform: scale(1.05);
}

.exhibition-status {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.status-active {
  background: rgba(82, 196, 26, 0.9);
}

.status-upcoming {
  background: rgba(24, 144, 255, 0.9);
}

.status-ended {
  background: rgba(140, 140, 140, 0.9);
}

.status-inactive {
  background: rgba(255, 77, 79, 0.9);
}

.status-text {
  color: #ffffff;
  font-size: 22rpx;
  font-weight: bold;
}

/* 展览信息 */
.exhibition-info {
  padding: 30rpx;
}

.exhibition-header {
  margin-bottom: 20rpx;
}

.exhibition-title {
  display: block;
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.3;
}

.exhibition-theme {
  display: inline-block;
  background: linear-gradient(135deg, #1890ff, #52c41a);
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
}

.theme-text {
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 500;
}

.exhibition-description {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 展览标签 */
.exhibition-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.tag {
  background: #f0f8ff;
  color: #1890ff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  border: 1rpx solid #d6e4ff;
}

/* 展览详情 */
.exhibition-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.detail-icon {
  font-size: 24rpx;
  width: 32rpx;
  text-align: center;
}

.detail-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

/* 展览统计 */
.exhibition-stats {
  display: flex;
  gap: 30rpx;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
}

.stat-icon {
  font-size: 20rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮 */
.exhibition-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

/* 加载状态 */
.load-more,
.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}

.no-more {
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}

/* 响应式适配 */
@media (max-width: 400px) {
  .exhibition-info {
    padding: 20rpx;
  }
  
  .exhibition-title {
    font-size: 30rpx;
  }
  
  .exhibition-description {
    font-size: 26rpx;
  }
  
  .exhibition-actions {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .exhibition-actions .btn {
    width: 100%;
  }
}
