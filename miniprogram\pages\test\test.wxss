/* pages/test/test.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255,255,255,0.8);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.info-card, .result-card {
  background: rgba(255,255,255,0.95);
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
}

.label, .result-title {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.value, .result-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  word-break: break-all;
  white-space: pre-line;
  margin-bottom: 20rpx;
}

.retry-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.deploy-card {
  background: rgba(255,255,255,0.95);
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  border: 2rpx solid #1890ff;
}

.deploy-title {
  display: block;
  font-size: 32rpx;
  color: #1890ff;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.deploy-steps {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.step {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.step-num {
  width: 50rpx;
  height: 50rpx;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-desc {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}
