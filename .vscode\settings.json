{"codingcopilot.enableWorkspaceRules": true, "augment.advanced": {"mcpServers": [{"name": "cloudbase", "command": "npx", "args": ["@cloudbase/cloudbase-mcp@latest"]}]}, "files.associations": {"*.wxml": "html", "*.wxss": "css"}, "emmet.includeLanguages": {"wxml": "html"}, "tasks.autoDetect": "on", "npm.enableScriptExplorer": true, "terminal.integrated.profiles.windows": {"CloudBase MCP": {"source": "PowerShell", "args": ["-NoExit", "-Command", "Write-Host '🚀 CloudBase MCP 环境已准备就绪！'; Write-Host '💡 运行以下命令:'; Write-Host '  - npm run start:mcp (启动 MCP)'; Write-Host '  - npm run check:mcp (检查状态)'; Write-Host '  - npm run start:cloudbase (检查云开发)'"]}}}