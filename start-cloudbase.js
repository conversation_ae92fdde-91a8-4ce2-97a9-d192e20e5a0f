#!/usr/bin/env node

/**
 * 云开发启动脚本
 * 一键启动云开发环境和服务
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 启动云开发环境');
console.log('=' .repeat(50));

// 配置信息
const config = {
  envId: 'danny-0g3qixz86747c5bb',
  appId: 'wxde0a110374e42908',
  projectName: '小程序+云开发空白项目'
};

console.log('📋 项目配置:');
console.log(`  - 项目名称: ${config.projectName}`);
console.log(`  - 小程序 AppID: ${config.appId}`);
console.log(`  - 云开发环境 ID: ${config.envId}`);

// 1. 检查项目结构
function checkProjectStructure() {
  console.log('\n📁 1. 检查项目结构');
  console.log('-'.repeat(30));
  
  const requiredDirs = [
    'miniprogram',
    'cloudfunctions',
    '.vscode'
  ];
  
  const requiredFiles = [
    'miniprogram/app.js',
    'miniprogram/app.json',
    'project.config.json',
    'cloudbaserc.json',
    '.vscode/mcp.json'
  ];
  
  // 检查目录
  requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      console.log(`  ✅ ${dir}/ 目录存在`);
    } else {
      console.log(`  ❌ ${dir}/ 目录缺失`);
    }
  });
  
  // 检查文件
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`  ✅ ${file} 文件存在`);
    } else {
      console.log(`  ❌ ${file} 文件缺失`);
    }
  });
}

// 2. 检查云函数状态
function checkCloudFunctions() {
  console.log('\n☁️  2. 检查云函数状态');
  console.log('-'.repeat(30));
  
  const functionsDir = path.join(process.cwd(), 'cloudfunctions');
  
  if (fs.existsSync(functionsDir)) {
    const functions = fs.readdirSync(functionsDir).filter(item => {
      const functionPath = path.join(functionsDir, item);
      return fs.statSync(functionPath).isDirectory();
    });
    
    console.log(`  📋 发现云函数: ${functions.join(', ')}`);
    
    functions.forEach(functionName => {
      const functionPath = path.join(functionsDir, functionName);
      const indexPath = path.join(functionPath, 'index.js');
      const packagePath = path.join(functionPath, 'package.json');
      
      console.log(`  📦 ${functionName}:`);
      console.log(`    - index.js: ${fs.existsSync(indexPath) ? '✅' : '❌'}`);
      console.log(`    - package.json: ${fs.existsSync(packagePath) ? '✅' : '❌'}`);
    });
  } else {
    console.log('  ❌ 云函数目录不存在');
  }
}

// 3. 检查 MCP 状态
function checkMCPStatus() {
  console.log('\n🔧 3. 检查 MCP 状态');
  console.log('-'.repeat(30));
  
  try {
    // 检查 CloudBase MCP 包
    const result = execSync('npm list -g @cloudbase/cloudbase-mcp', { 
      encoding: 'utf8', 
      timeout: 10000 
    });
    console.log('  ✅ CloudBase MCP 已安装');
    
    // 提取版本信息
    const versionMatch = result.match(/@cloudbase\/cloudbase-mcp@([\d\.]+)/);
    if (versionMatch) {
      console.log(`  📦 版本: ${versionMatch[1]}`);
    }
  } catch (error) {
    console.log('  ⚠️  CloudBase MCP 状态检查失败');
    console.log('  💡 建议: npm install -g @cloudbase/cloudbase-mcp@latest');
  }
}

// 4. 检查云开发登录状态
function checkLoginStatus() {
  console.log('\n🔐 4. 检查登录状态');
  console.log('-'.repeat(30));
  
  try {
    // 尝试使用 cloudbase CLI
    const result = execSync('cloudbase auth:list', { 
      encoding: 'utf8', 
      timeout: 10000 
    });
    console.log('  ✅ CloudBase CLI 已登录');
    console.log('  📋 登录信息:', result.trim());
  } catch (error) {
    console.log('  ⚠️  CloudBase CLI 未登录或有问题');
    console.log('  💡 建议: 在微信开发者工具中登录云开发');
  }
}

// 5. 启动建议
function provideStartupSuggestions() {
  console.log('\n🎯 5. 启动建议');
  console.log('-'.repeat(30));
  
  console.log('📱 微信开发者工具启动步骤:');
  console.log('  1. 打开微信开发者工具');
  console.log('  2. 导入项目 (选择当前目录)');
  console.log('  3. 点击"云开发"按钮');
  console.log('  4. 确认环境选择: danny-0g3qixz86747c5bb');
  console.log('  5. 编译并预览小程序');
  
  console.log('\n☁️  云函数部署步骤:');
  console.log('  1. 在云开发控制台中');
  console.log('  2. 右键 cloudfunctions/getOpenId');
  console.log('  3. 选择"创建并部署：云端安装依赖"');
  console.log('  4. 等待部署完成');
  
  console.log('\n🧪 功能测试步骤:');
  console.log('  1. 编译小程序');
  console.log('  2. 查看主页面状态');
  console.log('  3. 点击"环境测试"验证云函数');
  console.log('  4. 点击"OpenID 详情"查看用户信息');
}

// 6. 快速启动命令
function provideQuickCommands() {
  console.log('\n⚡ 6. 快速命令');
  console.log('-'.repeat(30));
  
  console.log('🔧 常用命令:');
  console.log('  # 查看云函数列表');
  console.log('  tcb functions:list');
  console.log('');
  console.log('  # 部署云函数');
  console.log('  tcb fn deploy getOpenId');
  console.log('');
  console.log('  # 查看环境信息');
  console.log('  tcb env:list');
  console.log('');
  console.log('  # 重新登录');
  console.log('  tcb login');
}

// 主函数
function main() {
  try {
    checkProjectStructure();
    checkCloudFunctions();
    checkMCPStatus();
    checkLoginStatus();
    provideStartupSuggestions();
    provideQuickCommands();
    
    console.log('\n🎉 云开发环境检查完成!');
    console.log('=' .repeat(50));
    console.log('💡 现在可以在微信开发者工具中启动项目了!');
    
  } catch (error) {
    console.error('❌ 启动检查过程中出现错误:', error.message);
  }
}

// 运行启动检查
main();
