// pages/registration/form/form.js
Page({
  data: {
    exhibitionId: '',
    exhibition: null,
    formData: {
      category: '',
      contactInfo: {
        name: '',
        phone: '',
        email: '',
        organization: '',
        position: ''
      },
      workInfo: {
        title: '',
        description: '',
        category: '',
        tags: []
      }
    },
    categories: [
      '智慧交通',
      '智慧建筑',
      '智慧社区',
      '智慧环保',
      '智慧医疗',
      '智慧教育',
      '其他'
    ],
    loading: false,
    submitting: false
  },

  onLoad(options) {
    if (options.exhibitionId) {
      this.setData({
        exhibitionId: options.exhibitionId
      });
      this.loadExhibitionInfo();
      this.loadUserInfo();
    }
  },

  // 加载展览信息
  async loadExhibitionInfo() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'content-management',
        data: {
          action: 'getExhibitionDetail',
          id: this.data.exhibitionId
        }
      });

      if (result.result.success) {
        this.setData({
          exhibition: result.result.data
        });
      }
    } catch (error) {
      console.error('加载展览信息失败:', error);
    }
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'user-management',
        data: {
          action: 'getUserInfo'
        }
      });

      if (result.result.success && result.result.data) {
        const user = result.result.data;
        this.setData({
          'formData.contactInfo.name': user.realName || user.nickName || '',
          'formData.contactInfo.phone': user.phone || '',
          'formData.contactInfo.email': user.email || '',
          'formData.contactInfo.organization': user.organization || '',
          'formData.contactInfo.position': user.profession || ''
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  },

  // 表单输入处理
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [field]: value
    });
  },

  // 分类选择
  onCategoryChange(e) {
    const index = e.detail.value;
    this.setData({
      'formData.category': this.data.categories[index]
    });
  },

  // 作品分类选择
  onWorkCategoryChange(e) {
    const index = e.detail.value;
    this.setData({
      'formData.workInfo.category': this.data.categories[index]
    });
  },

  // 标签输入处理
  onTagsInput(e) {
    const tags = e.detail.value.split(/[,，\s]+/).filter(tag => tag.trim());
    this.setData({
      'formData.workInfo.tags': tags
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.category) {
      wx.showToast({
        title: '请选择参赛类别',
        icon: 'none'
      });
      return false;
    }

    if (!formData.contactInfo.name) {
      wx.showToast({
        title: '请填写联系人姓名',
        icon: 'none'
      });
      return false;
    }

    if (!formData.contactInfo.phone) {
      wx.showToast({
        title: '请填写联系电话',
        icon: 'none'
      });
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.contactInfo.phone)) {
      wx.showToast({
        title: '请填写正确的手机号',
        icon: 'none'
      });
      return false;
    }

    // 验证邮箱格式（如果填写了）
    if (formData.contactInfo.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.contactInfo.email)) {
        wx.showToast({
          title: '请填写正确的邮箱地址',
          icon: 'none'
        });
        return false;
      }
    }

    if (!formData.workInfo.title) {
      wx.showToast({
        title: '请填写作品标题',
        icon: 'none'
      });
      return false;
    }

    if (!formData.workInfo.description) {
      wx.showToast({
        title: '请填写作品描述',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 提交报名
  async onSubmit() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    try {
      const result = await wx.cloud.callFunction({
        name: 'registration-system',
        data: {
          action: 'submitRegistration',
          registrationData: {
            exhibitionId: this.data.exhibitionId,
            category: this.data.formData.category,
            contactInfo: this.data.formData.contactInfo,
            workInfo: this.data.formData.workInfo
          }
        }
      });

      if (result.result.success) {
        wx.showToast({
          title: '报名提交成功',
          icon: 'success'
        });

        // 延迟跳转到状态查询页面
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/registration/status/status?exhibitionId=${this.data.exhibitionId}`
          });
        }, 1500);
      } else {
        throw new Error(result.result.error);
      }
    } catch (error) {
      console.error('提交报名失败:', error);
      wx.showToast({
        title: error.message || '提交失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 重置表单
  onReset() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置表单吗？所有填写的内容将被清空。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'formData.category': '',
            'formData.contactInfo.name': '',
            'formData.contactInfo.phone': '',
            'formData.contactInfo.email': '',
            'formData.contactInfo.organization': '',
            'formData.contactInfo.position': '',
            'formData.workInfo.title': '',
            'formData.workInfo.description': '',
            'formData.workInfo.category': '',
            'formData.workInfo.tags': []
          });
          wx.showToast({
            title: '表单已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  // 查看展览详情
  onViewExhibition() {
    wx.navigateTo({
      url: `/pages/exhibition/detail/detail?id=${this.data.exhibitionId}`
    });
  }
});
