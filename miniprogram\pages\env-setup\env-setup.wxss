/* pages/env-setup/env-setup.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

.section {
  background: white;
  margin-bottom: 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #eee;
}

.checking-box {
  background: #f0f8ff;
  border: 2rpx solid #91d5ff;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
}

.checking-text {
  display: block;
  color: #1890ff;
  font-size: 28rpx;
}

.success-box {
  background: #f6ffed;
  border: 2rpx solid #b7eb8f;
  border-radius: 10rpx;
  padding: 20rpx;
}

.success-text {
  display: block;
  color: #52c41a;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.success-desc {
  display: block;
  color: #666;
  font-size: 26rpx;
  margin-bottom: 8rpx;
}

.error-box {
  background: #fff2f0;
  border: 2rpx solid #ffccc7;
  border-radius: 10rpx;
  padding: 20rpx;
}

.error-text {
  display: block;
  color: #ff4d4f;
  font-size: 24rpx;
  font-family: monospace;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.error-desc {
  display: block;
  color: #666;
  font-size: 26rpx;
}

.steps {
  margin-bottom: 20rpx;
}

.step {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.step-text {
  font-size: 28rpx;
  color: #333;
}

.input-group {
  margin-bottom: 20rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  margin-bottom: 10rpx;
}

.input:focus {
  border-color: #1890ff;
}

.btn-copy {
  background: #f0f0f0;
  color: #666;
  border: none;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-left: 10rpx;
}

.btn-primary {
  width: 100%;
  background: #1890ff;
  color: white;
  border: none;
  padding: 25rpx;
  border-radius: 10rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.btn-primary:disabled {
  background: #d9d9d9;
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
  border: none;
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
}

.btn-success {
  width: 100%;
  background: #52c41a;
  color: white;
  border: none;
  padding: 25rpx;
  border-radius: 10rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.result-box {
  margin-top: 20rpx;
  padding: 20rpx;
  border-radius: 10rpx;
  background: #f6ffed;
  border: 2rpx solid #b7eb8f;
}

.result-text {
  font-size: 26rpx;
  color: #333;
  white-space: pre-line;
}

.config-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.code-box {
  background: #f5f5f5;
  border: 2rpx solid #d9d9d9;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.code {
  font-family: monospace;
  font-size: 24rpx;
  color: #333;
  white-space: pre;
}

.help-links {
  background: #f0f8ff;
  padding: 20rpx;
  border-radius: 10rpx;
  border: 2rpx solid #91d5ff;
}

.help-text {
  display: block;
  font-size: 26rpx;
  color: #1890ff;
  margin-bottom: 10rpx;
}

.env-info {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-top: 15rpx;
}

.env-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #e9ecef;
}

.env-item:last-child {
  border-bottom: none;
}

.env-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.env-value {
  font-size: 28rpx;
  color: #333;
  font-family: monospace;
}

.env-value.success {
  color: #52c41a;
  font-weight: bold;
}

.recheck-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}
