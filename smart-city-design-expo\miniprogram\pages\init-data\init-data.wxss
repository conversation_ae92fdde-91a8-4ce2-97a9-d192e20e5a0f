/* pages/init-data/init-data.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

/* 头部 */
.init-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.init-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
  display: block;
}

.init-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.init-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 内容区域 */
.init-content {
  width: 100%;
  max-width: 600rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
}

/* 初始化描述 */
.init-description {
  margin-bottom: 40rpx;
}

.desc-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.desc-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.desc-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding-left: 20rpx;
}

.desc-item {
  font-size: 26rpx;
  color: #1890ff;
  line-height: 1.5;
}

/* 成功信息 */
.success-info {
  text-align: center;
  margin-bottom: 40rpx;
}

.success-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
  display: block;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #52c41a;
  margin-bottom: 16rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 功能列表 */
.feature-list {
  margin-bottom: 40rpx;
}

.feature-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.feature-items {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.feature-icon {
  font-size: 32rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 操作按钮 */
.init-actions {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* 底部信息 */
.init-footer {
  text-align: center;
  margin-top: 60rpx;
}

.footer-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.footer-version {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 响应式适配 */
@media (max-width: 400px) {
  .init-content {
    padding: 30rpx;
  }
  
  .feature-items {
    grid-template-columns: 1fr;
  }
  
  .init-title {
    font-size: 40rpx;
  }
  
  .init-icon {
    font-size: 100rpx;
  }
}
