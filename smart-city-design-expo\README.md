# 🏙️ 智慧城市原创设计展小程序

## 📋 项目概述

基于智慧城市理念的原创设计展示与报名小程序，集成内容展示、作品投稿、在线报名等功能。

## 🎯 主要功能

### 1. 内容展示模块
- 智慧城市概念介绍
- 设计展主题展示
- 优秀作品展览
- 新闻资讯发布

### 2. 设计展报名模块
- 在线报名系统
- 作品上传功能
- 报名状态查询
- 评审结果公示

### 3. 用户互动模块
- 作品点赞评论
- 用户收藏功能
- 分享传播
- 消息通知

## 🏗️ 技术架构

- **前端**：微信小程序原生开发
- **后端**：腾讯云开发 CloudBase
- **数据库**：云数据库
- **存储**：云存储（作品文件）
- **云函数**：业务逻辑处理
- **MCP工具**：开发部署自动化

## 📱 页面结构

```
├── 首页 (index)
│   ├── 轮播图展示
│   ├── 快速入口
│   └── 热门作品
├── 展览 (exhibition)
│   ├── 主题展区
│   ├── 作品列表
│   └── 作品详情
├── 报名 (registration)
│   ├── 报名表单
│   ├── 作品上传
│   └── 状态查询
├── 资讯 (news)
│   ├── 新闻列表
│   └── 新闻详情
└── 个人 (profile)
    ├── 我的报名
    ├── 我的收藏
    └── 设置中心
```

## 🗄️ 数据库设计

### 核心数据表
- `users` - 用户信息
- `exhibitions` - 展览信息
- `works` - 作品信息
- `registrations` - 报名记录
- `news` - 新闻资讯
- `comments` - 评论数据
- `favorites` - 收藏记录

## 🚀 开发计划

详见任务分解列表...

## 📞 联系信息

- 环境ID: danny-0g3qixz86747c5bb
- 基础项目: ../miniprogram-cloudbase-miniprogram-template
