#!/usr/bin/env node

/**
 * 云开发启动检查脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 云开发启动检查');
console.log('=' .repeat(50));

// 项目配置
const config = {
  envId: 'danny-0g3qixz86747c5bb',
  appId: 'wxde0a110374e42908',
  projectName: '小程序+云开发空白项目'
};

console.log('📋 项目信息:');
console.log(`  项目名称: ${config.projectName}`);
console.log(`  小程序 AppID: ${config.appId}`);
console.log(`  云开发环境 ID: ${config.envId}`);

// 1. 检查项目文件
function checkProjectFiles() {
  console.log('\n📁 1. 项目文件检查');
  console.log('-'.repeat(30));
  
  const files = [
    { path: 'miniprogram/app.js', desc: '小程序主文件' },
    { path: 'miniprogram/app.json', desc: '小程序配置' },
    { path: 'project.config.json', desc: '项目配置' },
    { path: 'cloudbaserc.json', desc: '云开发配置' },
    { path: 'cloudfunctions/getOpenId/index.js', desc: '云函数代码' },
    { path: 'cloudfunctions/getOpenId/package.json', desc: '云函数依赖' },
    { path: '.vscode/mcp.json', desc: 'MCP 配置' }
  ];
  
  files.forEach(file => {
    const exists = fs.existsSync(file.path);
    const status = exists ? '✅' : '❌';
    console.log(`  ${status} ${file.desc}: ${file.path}`);
  });
}

// 2. 检查环境配置
function checkEnvironmentConfig() {
  console.log('\n🌍 2. 环境配置检查');
  console.log('-'.repeat(30));
  
  try {
    // 检查 app.js 中的环境配置
    const appJs = fs.readFileSync('miniprogram/app.js', 'utf8');
    const envMatch = appJs.match(/env:\s*['"`]([^'"`]+)['"`]/);
    
    if (envMatch) {
      const envId = envMatch[1];
      if (envId === config.envId) {
        console.log(`  ✅ 环境 ID 配置正确: ${envId}`);
      } else {
        console.log(`  ⚠️  环境 ID 不匹配: ${envId} (期望: ${config.envId})`);
      }
    } else {
      console.log('  ❌ 未找到环境 ID 配置');
    }
    
    // 检查 cloudbaserc.json
    const cloudbaserc = JSON.parse(fs.readFileSync('cloudbaserc.json', 'utf8'));
    if (cloudbaserc.envId === config.envId) {
      console.log(`  ✅ CloudBase 配置正确: ${cloudbaserc.envId}`);
    } else {
      console.log(`  ⚠️  CloudBase 配置不匹配: ${cloudbaserc.envId}`);
    }
    
  } catch (error) {
    console.log(`  ❌ 配置检查失败: ${error.message}`);
  }
}

// 3. 检查云函数
function checkCloudFunctions() {
  console.log('\n☁️  3. 云函数检查');
  console.log('-'.repeat(30));
  
  const functionsDir = 'cloudfunctions';
  
  if (fs.existsSync(functionsDir)) {
    const functions = fs.readdirSync(functionsDir).filter(item => {
      const functionPath = path.join(functionsDir, item);
      return fs.statSync(functionPath).isDirectory();
    });
    
    console.log(`  📋 发现云函数: ${functions.join(', ')}`);
    
    functions.forEach(functionName => {
      const functionPath = path.join(functionsDir, functionName);
      const indexPath = path.join(functionPath, 'index.js');
      const packagePath = path.join(functionPath, 'package.json');
      
      console.log(`  📦 ${functionName}:`);
      console.log(`    - 代码文件: ${fs.existsSync(indexPath) ? '✅' : '❌'}`);
      console.log(`    - 依赖配置: ${fs.existsSync(packagePath) ? '✅' : '❌'}`);
      
      if (fs.existsSync(packagePath)) {
        try {
          const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
          const deps = Object.keys(pkg.dependencies || {});
          console.log(`    - 依赖包: ${deps.join(', ')}`);
        } catch (error) {
          console.log(`    - 依赖解析失败: ${error.message}`);
        }
      }
    });
  } else {
    console.log('  ❌ 云函数目录不存在');
  }
}

// 4. 检查小程序页面
function checkMiniProgramPages() {
  console.log('\n📱 4. 小程序页面检查');
  console.log('-'.repeat(30));
  
  try {
    const appJson = JSON.parse(fs.readFileSync('miniprogram/app.json', 'utf8'));
    const pages = appJson.pages || [];
    
    console.log(`  📋 配置页面数量: ${pages.length}`);
    
    pages.forEach(page => {
      const pagePath = `miniprogram/${page}`;
      const jsFile = `${pagePath}.js`;
      const wxmlFile = `${pagePath}.wxml`;
      const wxssFile = `${pagePath}.wxss`;
      const jsonFile = `${pagePath}.json`;
      
      console.log(`  📄 ${page}:`);
      console.log(`    - JS: ${fs.existsSync(jsFile) ? '✅' : '❌'}`);
      console.log(`    - WXML: ${fs.existsSync(wxmlFile) ? '✅' : '❌'}`);
      console.log(`    - WXSS: ${fs.existsSync(wxssFile) ? '✅' : '❌'}`);
      console.log(`    - JSON: ${fs.existsSync(jsonFile) ? '✅' : '❌'}`);
    });
  } catch (error) {
    console.log(`  ❌ 页面检查失败: ${error.message}`);
  }
}

// 5. 启动指导
function provideStartupGuidance() {
  console.log('\n🎯 5. 启动指导');
  console.log('-'.repeat(30));
  
  console.log('📱 微信开发者工具启动步骤:');
  console.log('  1. 打开微信开发者工具');
  console.log('  2. 选择"导入项目"');
  console.log('  3. 项目目录: 选择当前文件夹');
  console.log(`  4. AppID: ${config.appId}`);
  console.log('  5. 项目名称: 可自定义');
  console.log('  6. 点击"导入"');
  
  console.log('\n☁️  云开发环境确认:');
  console.log('  1. 在开发者工具中点击"云开发"按钮');
  console.log(`  2. 确认环境选择: ${config.envId}`);
  console.log('  3. 如果云函数未部署，右键部署');
  
  console.log('\n🧪 功能测试:');
  console.log('  1. 编译小程序');
  console.log('  2. 查看主页面状态');
  console.log('  3. 点击"🧪 环境测试"验证云函数');
  console.log('  4. 点击"🔍 OpenID 详情"查看用户信息');
}

// 6. 成功标志
function showSuccessIndicators() {
  console.log('\n🎉 6. 成功标志');
  console.log('-'.repeat(30));
  
  console.log('✅ 启动成功的标志:');
  console.log('  - 主页面显示: "🎉 云开发环境正常"');
  console.log('  - OpenID 正常显示: "ox4f5w..."');
  console.log('  - 功能导航按钮可正常点击');
  console.log('  - 环境测试页面显示: "✅ 云函数调用成功"');
  
  console.log('\n⚠️  如果遇到问题:');
  console.log('  - 云函数调用失败: 检查云函数是否已部署');
  console.log('  - 环境连接失败: 确认网络和登录状态');
  console.log('  - 编译错误: 检查项目配置和 AppID');
}

// 主函数
function main() {
  try {
    checkProjectFiles();
    checkEnvironmentConfig();
    checkCloudFunctions();
    checkMiniProgramPages();
    provideStartupGuidance();
    showSuccessIndicators();
    
    console.log('\n🚀 云开发启动检查完成!');
    console.log('=' .repeat(50));
    console.log('💡 现在可以在微信开发者工具中启动项目了!');
    
  } catch (error) {
    console.error('❌ 启动检查过程中出现错误:', error.message);
  }
}

// 运行检查
main();
