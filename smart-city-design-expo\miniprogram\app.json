{"pages": ["pages/init-data/init-data", "pages/index/index", "pages/exhibition/list/list", "pages/exhibition/detail/detail", "pages/exhibition/works/works", "pages/registration/form/form", "pages/registration/upload/upload", "pages/registration/status/status", "pages/news/list/list", "pages/news/detail/detail", "pages/profile/profile", "pages/profile/my-registration/my-registration", "pages/profile/my-favorites/my-favorites", "pages/profile/settings/settings"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#1890ff", "navigationBarTitleText": "智慧城市设计展", "navigationBarTextStyle": "white", "backgroundColor": "#f5f5f5"}, "tabBar": {"color": "#666666", "selectedColor": "#1890ff", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "images/tab-home.png", "selectedIconPath": "images/tab-home-active.png"}, {"pagePath": "pages/exhibition/list/list", "text": "展览", "iconPath": "images/tab-exhibition.png", "selectedIconPath": "images/tab-exhibition-active.png"}, {"pagePath": "pages/registration/form/form", "text": "报名", "iconPath": "images/tab-registration.png", "selectedIconPath": "images/tab-registration-active.png"}, {"pagePath": "pages/news/list/list", "text": "资讯", "iconPath": "images/tab-news.png", "selectedIconPath": "images/tab-news-active.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "images/tab-profile.png", "selectedIconPath": "images/tab-profile-active.png"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": true, "navigateToMiniProgramAppIdList": [], "usingComponents": {"work-card": "/components/work-card/work-card", "news-item": "/components/news-item/news-item", "upload-panel": "/components/upload-panel/upload-panel", "status-badge": "/components/status-badge/status-badge"}, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于展示附近的展览活动"}}, "requiredBackgroundModes": ["audio"], "plugins": {}, "resizable": true, "sitemapLocation": "sitemap.json", "style": "v2", "lazyCodeLoading": "requiredComponents"}