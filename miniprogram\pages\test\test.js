// pages/test/test.js
Page({
  data: {
    envId: 'danny-0g3qixz86747c5bb',
    testResult: ''
  },

  onLoad() {
    console.log('测试页面加载成功');
    this.testCloudFunction();
  },

  async testCloudFunction() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getOpenId'
      });

      this.setData({
        testResult: `✅ 云函数调用成功！\nOpenID: ${result.result.openid}`
      });

      wx.showToast({
        title: '环境配置成功！',
        icon: 'success'
      });

    } catch (error) {
      console.error('云函数调用失败:', error);

      let errorMsg = error.errMsg || error.message;
      let solution = '';

      if (errorMsg.includes('FunctionName parameter could not be found')) {
        solution = '\n\n🔧 解决方案：\n1. 在微信开发者工具中\n2. 右键点击 cloudfunctions/getOpenId\n3. 选择"创建并部署：云端安装依赖"\n4. 等待部署完成后重试';
      }

      this.setData({
        testResult: `❌ 云函数调用失败：\n${errorMsg}${solution}`
      });
    }
  },

  // 重新测试
  retryTest() {
    this.setData({
      testResult: '重新测试中...'
    });
    this.testCloudFunction();
  }
});
