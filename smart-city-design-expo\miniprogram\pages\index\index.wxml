<!--pages/index/index.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>🔄 加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <!-- 轮播图 -->
    <view class="banner-section" wx:if="{{banners.length > 0}}">
      <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{5000}}" duration="{{500}}">
        <swiper-item wx:for="{{banners}}" wx:key="_id" bindtap="onBannerTap" data-item="{{item}}">
          <image class="banner-image" src="{{item.image}}" mode="aspectFill" />
          <view class="banner-overlay">
            <text class="banner-title">{{item.title}}</text>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 快速入口 -->
    <view class="quick-entry-section">
      <view class="section-title">🛠️ 快速入口</view>
      <view class="quick-entry-grid">
        <view class="quick-entry-item" bindtap="onQuickEntryTap" data-type="exhibition">
          <view class="quick-entry-icon">🏛️</view>
          <text class="quick-entry-text">展览浏览</text>
        </view>
        <view class="quick-entry-item" bindtap="onQuickEntryTap" data-type="registration">
          <view class="quick-entry-icon">📝</view>
          <text class="quick-entry-text">在线报名</text>
        </view>
        <view class="quick-entry-item" bindtap="onQuickEntryTap" data-type="news">
          <view class="quick-entry-icon">📰</view>
          <text class="quick-entry-text">最新资讯</text>
        </view>
        <view class="quick-entry-item" bindtap="onQuickEntryTap" data-type="profile">
          <view class="quick-entry-icon">👤</view>
          <text class="quick-entry-text">个人中心</text>
        </view>
      </view>
    </view>

    <!-- 热门作品 -->
    <view class="hot-works-section" wx:if="{{hotWorks.length > 0}}">
      <view class="section-header">
        <text class="section-title">🔥 热门作品</text>
        <text class="section-more" bindtap="onMoreHotWorks">查看更多 ></text>
      </view>
      <scroll-view class="works-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
        <view class="works-list">
          <view class="work-card" wx:for="{{hotWorks}}" wx:key="_id" bindtap="onWorkTap" data-id="{{item._id}}">
            <image class="work-image" src="{{item.images[0]}}" mode="aspectFill" />
            <view class="work-info">
              <text class="work-title">{{item.title}}</text>
              <text class="work-author">{{item.authorName}}</text>
              <view class="work-stats">
                <text class="work-stat">👍 {{item.likeCount}}</text>
                <text class="work-stat">👁️ {{item.viewCount}}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 活跃展览 -->
    <view class="exhibitions-section" wx:if="{{exhibitions.length > 0}}">
      <view class="section-title">🏛️ 活跃展览</view>
      <view class="exhibitions-list">
        <view class="exhibition-card" wx:for="{{exhibitions}}" wx:key="_id" bindtap="onExhibitionTap" data-id="{{item._id}}">
          <image class="exhibition-image" src="{{item.coverImage}}" mode="aspectFill" />
          <view class="exhibition-info">
            <text class="exhibition-title">{{item.title}}</text>
            <text class="exhibition-desc">{{item.description}}</text>
            <view class="exhibition-meta">
              <text class="exhibition-location">📍 {{item.location}}</text>
              <text class="exhibition-time">⏰ {{item.startDate}}</text>
            </view>
            <view class="exhibition-stats">
              <text class="exhibition-stat">👥 {{item.registrationCount}}人报名</text>
              <text class="exhibition-stat">👁️ {{item.viewCount}}次浏览</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 最新资讯 -->
    <view class="news-section" wx:if="{{latestNews.length > 0}}">
      <view class="section-header">
        <text class="section-title">📰 最新资讯</text>
        <text class="section-more" bindtap="onMoreNews">查看更多 ></text>
      </view>
      <view class="news-list">
        <view class="news-item" wx:for="{{latestNews}}" wx:key="_id" bindtap="onNewsTap" data-id="{{item._id}}">
          <image class="news-image" src="{{item.coverImage}}" mode="aspectFill" />
          <view class="news-info">
            <text class="news-title">{{item.title}}</text>
            <text class="news-summary">{{item.summary}}</text>
            <view class="news-meta">
              <text class="news-author">{{item.author}}</text>
              <text class="news-time">{{item.publishTime}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:if="{{!loading && banners.length === 0 && hotWorks.length === 0 && exhibitions.length === 0 && latestNews.length === 0}}">
      <view class="empty-icon">📱</view>
      <text class="empty-text">暂无内容，请稍后再试</text>
      <button class="btn btn-primary mt-20" bindtap="loadPageData">重新加载</button>
    </view>
  </view>
</view>
