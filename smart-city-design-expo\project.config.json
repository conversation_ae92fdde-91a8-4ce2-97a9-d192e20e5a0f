{"description": "智慧城市原创设计展小程序", "packOptions": {"ignore": [], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useStaticServer": true, "checkInvalidKey": true, "disableUseStrict": false, "useCompilerPlugins": false, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.8.9", "appid": "wxde0a110374e42908", "projectname": "smart-city-design-expo", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "cloudfunctionRoot": "cloudfunctions/", "miniprogramRoot": "miniprogram/", "qcloudRoot": "./", "pluginRoot": "", "isGameTourist": false, "simulatorType": "wechat", "simulatorPluginLibVersion": {}}