<view class="container">
  <view class="header">
    <text class="title">CloudBase 小程序模板</text>
    <text class="subtitle">基于微信云开发的小程序应用</text>
  </view>
  
  <view class="content">
    <!-- 成功状态卡片 -->
    <view class="success-card" wx:if="{{openid}}">
      <text class="success-title">🎉 云开发环境正常</text>
      <text class="success-desc">云函数调用成功，环境配置正确</text>
    </view>

    <!-- 环境配置提示 -->
    <view class="error-card" wx:if="{{!openid && hasError}}">
      <text class="error-title">⚠️ 环境配置问题</text>
      <text class="error-desc">云函数调用失败，可能是环境 ID 配置错误</text>
      <button class="fix-btn" bindtap="goToEnvSetup">🔧 修复配置</button>
    </view>

    <view class="info-card">
      <text class="label">您的OpenID：</text>
      <text class="value">{{openid || '获取中...'}}</text>
    </view>
    
    <view class="features">
      <text class="features-title">✨ 项目特点</text>
      <view class="feature-item">🔐 云开发身份认证</view>
      <view class="feature-item">☁️ 云函数调用能力</view>
      <view class="feature-item">📱 原生小程序开发</view>
      <view class="feature-item">🚀 一键部署上线</view>
    </view>

    <!-- 功能导航 -->
    <view class="nav-section">
      <text class="nav-title">🛠️ 功能导航</text>
      <view class="nav-buttons">
        <button class="nav-btn test-btn" bindtap="goToTest">
          <text class="nav-btn-icon">🧪</text>
          <text class="nav-btn-text">环境测试</text>
        </button>
        <button class="nav-btn setup-btn" bindtap="goToEnvSetup">
          <text class="nav-btn-icon">⚙️</text>
          <text class="nav-btn-text">环境配置</text>
        </button>
      </view>
    </view>
  </view>
  
  <view class="footer">
    <cloudbase-badge show-text="{{true}}" />
  </view>
</view>