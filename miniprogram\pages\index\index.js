Page({
  data: {
    openid: '',
    hasError: false
  },

  onLoad() {
    this.getOpenId();
  },

  // 获取 OpenID
  async getOpenId() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getOpenId'
      });

      this.setData({
        openid: res.result.openid,
        hasError: false
      });
    } catch (err) {
      console.error('调用云函数失败', err);

      // 检查是否是环境配置错误
      if (err.errCode === -501000 || err.errMsg.includes('env check invalid')) {
        this.setData({
          hasError: true
        });

        wx.showToast({
          title: '环境配置错误',
          icon: 'none',
          duration: 2000
        });
      }
    }
  },

  // 跳转到环境配置页面
  goToEnvSetup() {
    wx.navigateTo({
      url: '/pages/env-setup/env-setup'
    });
  },

  // 跳转到测试页面
  goToTest() {
    wx.navigateTo({
      url: '/pages/test/test'
    });
  }
})