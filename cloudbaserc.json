{"envId": "danny-0g3qixz86747c5bb", "version": "2.0", "framework": {"name": "miniprogram", "plugins": {"client": {"use": "@cloudbase/framework-plugin-mp", "inputs": {"appid": "wxde0a110374e42908", "projectPath": "./miniprogram"}}, "functions": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "./cloudfunctions", "functions": [{"name": "getOpenId", "timeout": 5, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 128}]}}}}}