#!/usr/bin/env node

/**
 * 使用 CloudBase MCP 工具部署云函数
 */

const path = require('path');

console.log('🚀 使用 CloudBase MCP 工具部署云函数...');

const envId = 'danny-0g3qixz86747c5bb';
const functionRootPath = path.resolve(__dirname, 'cloudfunctions');
const functionName = 'getOpenId';

console.log('📋 部署参数:');
console.log('- 环境 ID:', envId);
console.log('- 云函数根目录:', functionRootPath);
console.log('- 函数名称:', functionName);

// 根据文档，MCP 工具应该通过特定的接口调用
// 由于我无法直接调用 MCP 工具，我将创建一个模拟的部署流程

console.log('\n🔧 MCP 工具部署流程:');
console.log('1. listFunctions - 查询当前云函数列表');
console.log('2. createFunction - 创建/更新云函数');
console.log('3. functionRootPath 指向:', functionRootPath);

// 检查云函数文件
const fs = require('fs');
const functionPath = path.join(functionRootPath, functionName);

if (fs.existsSync(functionPath)) {
  console.log('✅ 云函数目录存在:', functionPath);
  
  const indexPath = path.join(functionPath, 'index.js');
  const packagePath = path.join(functionPath, 'package.json');
  
  if (fs.existsSync(indexPath)) {
    console.log('✅ index.js 存在');
  }
  
  if (fs.existsSync(packagePath)) {
    console.log('✅ package.json 存在');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    console.log('📦 依赖:', Object.keys(packageJson.dependencies || {}));
  }
} else {
  console.log('❌ 云函数目录不存在:', functionPath);
}

console.log('\n💡 MCP 工具调用方式:');
console.log('根据文档，应该使用以下 MCP 工具:');
console.log('- listFunctions() - 查询云函数列表');
console.log('- createFunction(functionRootPath, functionName) - 创建云函数');
console.log('- updateFunctionCode(functionRootPath, functionName) - 更新云函数代码');

console.log('\n🎯 下一步:');
console.log('1. 确保 MCP server 正在运行');
console.log('2. 通过 AI Agent 调用 MCP 工具');
console.log('3. 或在微信开发者工具中手动部署');

// 尝试使用 CloudBase CLI 作为备选方案
console.log('\n🔄 备选方案: 使用 CloudBase CLI');
const { execSync } = require('child_process');

try {
  // 检查是否已登录
  console.log('🔍 检查登录状态...');
  const loginStatus = execSync('cloudbase auth:list', { encoding: 'utf8', timeout: 10000 });
  console.log('✅ 登录状态:', loginStatus);
  
  // 尝试部署
  console.log('🚀 尝试部署云函数...');
  const deployResult = execSync(`cloudbase functions:deploy ${functionName} --env ${envId}`, { 
    encoding: 'utf8', 
    timeout: 60000,
    cwd: __dirname
  });
  console.log('✅ 部署成功:', deployResult);
  
} catch (error) {
  console.log('⚠️  CloudBase CLI 部署失败:', error.message);
  console.log('💡 请使用微信开发者工具手动部署');
}
