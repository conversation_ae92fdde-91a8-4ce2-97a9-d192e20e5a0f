#!/usr/bin/env node

/**
 * 测试 CloudBase MCP 工具是否可用
 */

console.log('🔍 测试 CloudBase MCP 工具...');

// 检查环境变量
console.log('📋 环境信息:');
console.log('- Node.js 版本:', process.version);
console.log('- 当前目录:', process.cwd());
console.log('- 环境 ID: danny-0g3qixz86747c5bb');

// 检查 MCP 配置
const fs = require('fs');
const path = require('path');

const mcpConfigPath = path.join(process.cwd(), '.vscode', 'mcp.json');
if (fs.existsSync(mcpConfigPath)) {
  console.log('✅ MCP 配置文件存在');
  const mcpConfig = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'));
  console.log('📄 MCP 配置:', JSON.stringify(mcpConfig, null, 2));
} else {
  console.log('❌ MCP 配置文件不存在');
}

// 检查云函数目录
const functionsDir = path.join(process.cwd(), 'cloudfunctions');
if (fs.existsSync(functionsDir)) {
  console.log('✅ 云函数目录存在');
  const functions = fs.readdirSync(functionsDir).filter(item => {
    const functionPath = path.join(functionsDir, item);
    return fs.statSync(functionPath).isDirectory();
  });
  console.log('📋 发现云函数:', functions);
} else {
  console.log('❌ 云函数目录不存在');
}

console.log('\n🎯 下一步:');
console.log('1. 确保 CloudBase MCP 工具已正确安装');
console.log('2. 使用 MCP 工具部署云函数');
console.log('3. 测试云函数调用');

// 尝试检查 CloudBase MCP 包
const { execSync } = require('child_process');

try {
  console.log('\n🔧 检查 CloudBase MCP 包...');
  const result = execSync('npm list @cloudbase/cloudbase-mcp', { encoding: 'utf8', timeout: 10000 });
  console.log('✅ CloudBase MCP 包状态:', result);
} catch (error) {
  console.log('⚠️  CloudBase MCP 包未安装或有问题');
  console.log('💡 建议: npm install -g @cloudbase/cloudbase-mcp@latest');
}
