#!/usr/bin/env node

/**
 * 云函数部署脚本
 * 使用 CloudBase CLI 部署云函数
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

const ENV_ID = 'danny-0g3qixz86747c5bb';
const FUNCTIONS_DIR = path.join(__dirname, 'cloudfunctions');

console.log('🚀 开始部署云函数...');
console.log(`📁 云函数目录: ${FUNCTIONS_DIR}`);
console.log(`🌍 环境 ID: ${ENV_ID}`);

// 检查云函数目录是否存在
if (!fs.existsSync(FUNCTIONS_DIR)) {
  console.error('❌ 云函数目录不存在:', FUNCTIONS_DIR);
  process.exit(1);
}

// 获取所有云函数
const functions = fs.readdirSync(FUNCTIONS_DIR).filter(item => {
  const functionPath = path.join(FUNCTIONS_DIR, item);
  return fs.statSync(functionPath).isDirectory();
});

console.log(`📋 发现云函数: ${functions.join(', ')}`);

// 部署每个云函数
for (const functionName of functions) {
  console.log(`\n🔧 部署云函数: ${functionName}`);
  
  try {
    const functionPath = path.join(FUNCTIONS_DIR, functionName);
    
    // 检查是否有 package.json
    const packageJsonPath = path.join(functionPath, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      console.log(`⚠️  ${functionName} 缺少 package.json，创建默认配置...`);
      
      const defaultPackageJson = {
        "name": functionName,
        "version": "1.0.0",
        "description": "",
        "main": "index.js",
        "dependencies": {
          "wx-server-sdk": "~2.6.3"
        }
      };
      
      fs.writeFileSync(packageJsonPath, JSON.stringify(defaultPackageJson, null, 2));
    }
    
    // 使用 CloudBase CLI 部署
    const deployCommand = `npx @cloudbase/cli@latest functions:deploy ${functionName} --env ${ENV_ID}`;
    console.log(`📤 执行命令: ${deployCommand}`);
    
    execSync(deployCommand, { 
      stdio: 'inherit',
      cwd: __dirname
    });
    
    console.log(`✅ ${functionName} 部署成功！`);
    
  } catch (error) {
    console.error(`❌ ${functionName} 部署失败:`, error.message);
  }
}

console.log('\n🎉 云函数部署完成！');
console.log('\n📱 现在可以在小程序中测试云函数调用了。');
