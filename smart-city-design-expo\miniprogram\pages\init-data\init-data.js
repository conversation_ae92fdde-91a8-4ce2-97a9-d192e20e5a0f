// pages/init-data/init-data.js
Page({
  data: {
    loading: false,
    initialized: false
  },

  onLoad() {
    // 检查是否已经初始化过数据
    this.checkDataStatus();
  },

  // 检查数据状态
  async checkDataStatus() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'content-management',
        data: {
          action: 'getBanners'
        }
      });

      if (result.result.success && result.result.data.length > 0) {
        this.setData({ initialized: true });
      }
    } catch (error) {
      console.error('检查数据状态失败:', error);
    }
  },

  // 初始化模拟数据
  async initMockData() {
    this.setData({ loading: true });

    try {
      wx.showLoading({
        title: '初始化数据中...'
      });

      const result = await wx.cloud.callFunction({
        name: 'content-management',
        data: {
          action: 'initMockData'
        }
      });

      if (result.result.success) {
        this.setData({ 
          initialized: true,
          loading: false 
        });
        
        wx.hideLoading();
        wx.showToast({
          title: '数据初始化成功',
          icon: 'success'
        });

        // 延迟跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
      } else {
        throw new Error(result.result.error);
      }
    } catch (error) {
      console.error('初始化数据失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '初始化失败: ' + error.message,
        icon: 'none',
        duration: 3000
      });
      this.setData({ loading: false });
    }
  },

  // 跳转到首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 重新初始化
  reinitData() {
    wx.showModal({
      title: '确认重新初始化',
      content: '这将清空现有数据并重新创建模拟数据，确定继续吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ initialized: false });
          this.initMockData();
        }
      }
    });
  }
});
