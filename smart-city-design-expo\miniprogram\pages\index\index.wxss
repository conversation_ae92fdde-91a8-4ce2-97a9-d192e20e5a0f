/* pages/index/index.wxss */

/* 轮播图样式 */
.banner-section {
  margin-bottom: 30rpx;
}

.banner-swiper {
  width: 100%;
  height: 400rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 30rpx 30rpx;
}

.banner-title {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 快速入口样式 */
.quick-entry-section {
  margin-bottom: 40rpx;
}

.quick-entry-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.quick-entry-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.quick-entry-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.quick-entry-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.quick-entry-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 区块标题样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-more {
  font-size: 26rpx;
  color: #1890ff;
}

/* 热门作品样式 */
.hot-works-section {
  margin-bottom: 40rpx;
}

.works-scroll {
  white-space: nowrap;
}

.works-list {
  display: inline-flex;
  gap: 20rpx;
  padding: 0 20rpx 20rpx 0;
}

.work-card {
  width: 280rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.work-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

.work-image {
  width: 100%;
  height: 200rpx;
}

.work-info {
  padding: 20rpx;
}

.work-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.work-author {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.work-stats {
  display: flex;
  gap: 20rpx;
}

.work-stat {
  font-size: 22rpx;
  color: #999;
}

/* 展览样式 */
.exhibitions-section {
  margin-bottom: 40rpx;
}

.exhibitions-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.exhibition-card {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.exhibition-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 25rpx rgba(0, 0, 0, 0.1);
}

.exhibition-image {
  width: 100%;
  height: 300rpx;
}

.exhibition-info {
  padding: 30rpx;
}

.exhibition-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.exhibition-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.exhibition-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.exhibition-location,
.exhibition-time {
  font-size: 24rpx;
  color: #999;
}

.exhibition-stats {
  display: flex;
  gap: 30rpx;
}

.exhibition-stat {
  font-size: 24rpx;
  color: #1890ff;
}

/* 新闻样式 */
.news-section {
  margin-bottom: 40rpx;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.news-item {
  display: flex;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.news-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 25rpx rgba(0, 0, 0, 0.1);
}

.news-image {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}

.news-info {
  flex: 1;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-summary {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.news-author,
.news-time {
  font-size: 22rpx;
  color: #999;
}

/* 响应式适配 */
@media (max-width: 400px) {
  .quick-entry-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15rpx;
  }
  
  .quick-entry-item {
    padding: 25rpx 15rpx;
  }
  
  .quick-entry-icon {
    font-size: 40rpx;
  }
  
  .quick-entry-text {
    font-size: 22rpx;
  }
}
