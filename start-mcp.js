#!/usr/bin/env node

/**
 * MCP 服务快速启动脚本
 * 一键启动所有相关的 MCP 服务
 */

const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 MCP 服务快速启动');
console.log('=' .repeat(50));

// 配置信息
const config = {
  envId: 'danny-0g3qixz86747c5bb',
  appId: 'wxde0a110374e42908',
  mcpServices: [
    {
      name: 'CloudBase MCP',
      command: 'npx',
      args: ['-y', '@cloudbase/cloudbase-mcp@latest'],
      description: '云开发 MCP 服务器'
    }
  ]
};

// 1. 检查环境
function checkEnvironment() {
  console.log('\n🔍 1. 环境检查');
  console.log('-'.repeat(30));
  
  // 检查 Node.js
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    console.log(`  ✅ Node.js: ${nodeVersion}`);
  } catch (error) {
    console.log('  ❌ Node.js 未安装或不可用');
    return false;
  }
  
  // 检查 npm
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    console.log(`  ✅ npm: ${npmVersion}`);
  } catch (error) {
    console.log('  ❌ npm 未安装或不可用');
    return false;
  }
  
  // 检查项目文件
  const requiredFiles = [
    '.vscode/mcp.json',
    'cloudbaserc.json',
    'miniprogram/app.js'
  ];
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`  ✅ ${file}`);
    } else {
      console.log(`  ❌ ${file} 缺失`);
    }
  });
  
  return true;
}

// 2. 检查 MCP 配置
function checkMCPConfig() {
  console.log('\n📋 2. MCP 配置检查');
  console.log('-'.repeat(30));
  
  try {
    const mcpConfig = JSON.parse(fs.readFileSync('.vscode/mcp.json', 'utf8'));
    console.log('  ✅ MCP 配置文件存在');
    
    if (mcpConfig.servers) {
      Object.keys(mcpConfig.servers).forEach(serverName => {
        const server = mcpConfig.servers[serverName];
        console.log(`  📡 ${serverName}: ${server.command} ${server.args ? server.args.join(' ') : ''}`);
      });
    }
    
    return true;
  } catch (error) {
    console.log('  ❌ MCP 配置文件读取失败:', error.message);
    return false;
  }
}

// 3. 检查 CloudBase MCP 包
function checkCloudBaseMCP() {
  console.log('\n📦 3. CloudBase MCP 包检查');
  console.log('-'.repeat(30));
  
  try {
    // 检查全局安装
    const result = execSync('npm list -g @cloudbase/cloudbase-mcp', { 
      encoding: 'utf8', 
      timeout: 10000 
    });
    console.log('  ✅ CloudBase MCP 已全局安装');
    
    // 提取版本信息
    const versionMatch = result.match(/@cloudbase\/cloudbase-mcp@([\d\.]+)/);
    if (versionMatch) {
      console.log(`  📦 版本: ${versionMatch[1]}`);
    }
    
    return true;
  } catch (error) {
    console.log('  ⚠️  CloudBase MCP 未全局安装，将使用 npx 自动下载');
    return true; // npx 可以自动下载，所以返回 true
  }
}

// 4. 启动 MCP 服务
function startMCPServices() {
  console.log('\n🚀 4. 启动 MCP 服务');
  console.log('-'.repeat(30));
  
  const processes = [];
  
  config.mcpServices.forEach((service, index) => {
    console.log(`  🔄 启动 ${service.name}...`);
    
    try {
      const child = spawn(service.command, service.args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true,
        detached: false
      });
      
      // 处理输出
      child.stdout.on('data', (data) => {
        console.log(`  📡 [${service.name}] ${data.toString().trim()}`);
      });
      
      child.stderr.on('data', (data) => {
        console.log(`  ⚠️  [${service.name}] ${data.toString().trim()}`);
      });
      
      child.on('close', (code) => {
        console.log(`  🔴 [${service.name}] 进程退出，代码: ${code}`);
      });
      
      child.on('error', (error) => {
        console.log(`  ❌ [${service.name}] 启动失败: ${error.message}`);
      });
      
      processes.push({
        name: service.name,
        process: child,
        pid: child.pid
      });
      
      console.log(`  ✅ ${service.name} 已启动 (PID: ${child.pid})`);
      
    } catch (error) {
      console.log(`  ❌ ${service.name} 启动失败: ${error.message}`);
    }
  });
  
  return processes;
}

// 5. 显示启动结果
function showStartupResult(processes) {
  console.log('\n🎉 5. 启动结果');
  console.log('-'.repeat(30));
  
  if (processes.length > 0) {
    console.log('  ✅ MCP 服务启动成功！');
    console.log('\n  📋 运行中的服务:');
    processes.forEach(proc => {
      console.log(`    - ${proc.name} (PID: ${proc.pid})`);
    });
    
    console.log('\n  💡 使用说明:');
    console.log('    - MCP 服务现在正在后台运行');
    console.log('    - 在支持 MCP 的 AI 工具中可以调用云开发功能');
    console.log('    - 关闭终端会停止 MCP 服务');
    
    console.log('\n  🔧 可用的 MCP 工具:');
    console.log('    - listFunctions - 查询云函数列表');
    console.log('    - createFunction - 创建/部署云函数');
    console.log('    - updateFunctionCode - 更新云函数代码');
    console.log('    - uploadFiles - 上传静态文件');
    console.log('    - getEnvInfo - 获取环境信息');
    
  } else {
    console.log('  ❌ 没有成功启动任何 MCP 服务');
  }
}

// 6. 处理退出
function setupExitHandlers(processes) {
  const cleanup = () => {
    console.log('\n🔄 正在停止 MCP 服务...');
    processes.forEach(proc => {
      try {
        process.kill(proc.process.pid);
        console.log(`  ✅ 已停止 ${proc.name}`);
      } catch (error) {
        console.log(`  ⚠️  停止 ${proc.name} 失败: ${error.message}`);
      }
    });
    process.exit(0);
  };
  
  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);
  process.on('exit', cleanup);
}

// 主函数
async function main() {
  try {
    // 检查环境
    if (!checkEnvironment()) {
      console.log('\n❌ 环境检查失败，请检查 Node.js 和 npm 安装');
      return;
    }
    
    // 检查 MCP 配置
    if (!checkMCPConfig()) {
      console.log('\n❌ MCP 配置检查失败，请检查 .vscode/mcp.json 文件');
      return;
    }
    
    // 检查 CloudBase MCP
    checkCloudBaseMCP();
    
    // 启动服务
    const processes = startMCPServices();
    
    // 显示结果
    showStartupResult(processes);
    
    // 设置退出处理
    if (processes.length > 0) {
      setupExitHandlers(processes);
      
      console.log('\n⌨️  按 Ctrl+C 停止所有服务');
      
      // 保持进程运行
      setInterval(() => {
        // 检查进程是否还在运行
        processes.forEach(proc => {
          if (proc.process.killed) {
            console.log(`  ⚠️  ${proc.name} 进程已停止`);
          }
        });
      }, 30000); // 每30秒检查一次
    }
    
  } catch (error) {
    console.error('\n❌ 启动过程中出现错误:', error.message);
  }
}

// 运行主函数
main();
