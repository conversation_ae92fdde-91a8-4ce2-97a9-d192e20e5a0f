.container {
  min-height: 100vh;
  padding: 40rpx 60rpx;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.title {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.subtitle {
  display: block;
  font-size: 32rpx;
  color: rgba(255,255,255,0.8);
  font-weight: 300;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 60rpx;
}

.error-card {
  background: rgba(255,245,245,0.95);
  border: 2rpx solid #ffccc7;
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(255,77,79,0.1);
  backdrop-filter: blur(10rpx);
  margin-bottom: 30rpx;
}

.error-title {
  display: block;
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.error-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.success-card {
  background: rgba(245,255,245,0.95);
  border: 2rpx solid #b7eb8f;
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(82,196,26,0.1);
  backdrop-filter: blur(10rpx);
  margin-bottom: 30rpx;
}

.success-title {
  display: block;
  font-size: 32rpx;
  color: #52c41a;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.fix-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.info-card {
  background: rgba(255,255,255,0.95);
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  backdrop-filter: blur(10rpx);
}

.label {
  display: block;
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.value {
  display: block;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  word-break: break-all;
  font-family: 'Monaco', 'Menlo', monospace;
}

.features {
  background: rgba(255,255,255,0.95);
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  backdrop-filter: blur(10rpx);
}

.features-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.feature-item {
  font-size: 30rpx;
  color: #555;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  line-height: 1.6;
}

.nav-section {
  background: rgba(255,255,255,0.95);
  padding: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
  backdrop-filter: blur(10rpx);
  margin-top: 30rpx;
}

.nav-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.nav-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: space-between;
}

.nav-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.test-btn {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
}

.setup-btn {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  color: white;
}

.nav-btn-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.nav-btn-text {
  font-size: 26rpx;
}

.footer {
  text-align: center;
  margin-top: 60rpx;
  padding-top: 40rpx;
}

/* badge样式已移到组件中 */
