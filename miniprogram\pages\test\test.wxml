<!--pages/test/test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">🧪 环境测试</text>
    <text class="subtitle">测试云开发环境配置</text>
  </view>

  <view class="content">
    <view class="info-card">
      <text class="label">环境 ID：</text>
      <text class="value">{{envId}}</text>
    </view>

    <view class="result-card">
      <text class="result-title">测试结果：</text>
      <text class="result-text" user-select>{{testResult || '测试中...'}}</text>
      <button class="retry-btn" bindtap="retryTest" wx:if="{{testResult && testResult.indexOf('❌') === 0}}">🔄 重新测试</button>
    </view>
  </view>
</view>
