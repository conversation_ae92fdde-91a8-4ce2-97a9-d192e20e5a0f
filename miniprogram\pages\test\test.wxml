<!--pages/test/test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">🧪 环境测试</text>
    <text class="subtitle">测试云开发环境配置</text>
  </view>

  <view class="content">
    <view class="info-card">
      <text class="label">环境 ID：</text>
      <text class="value">{{envId}}</text>
    </view>

    <view class="result-card">
      <text class="result-title">测试结果：</text>
      <text class="result-text" user-select>{{testResult || '测试中...'}}</text>
      <button class="retry-btn" bindtap="retryTest" wx:if="{{testResult && testResult.indexOf('❌') === 0}}">🔄 重新测试</button>
    </view>

    <!-- 部署指导卡片 -->
    <view class="deploy-card" wx:if="{{testResult && testResult.indexOf('FunctionName parameter could not be found') > -1}}">
      <text class="deploy-title">🚀 云函数部署指导</text>
      <view class="deploy-steps">
        <view class="step">
          <text class="step-num">1</text>
          <text class="step-desc">在微信开发者工具中点击"云开发"按钮</text>
        </view>
        <view class="step">
          <text class="step-num">2</text>
          <text class="step-desc">确认环境选择为：{{envId}}</text>
        </view>
        <view class="step">
          <text class="step-num">3</text>
          <text class="step-desc">在左侧文件树找到 cloudfunctions/getOpenId</text>
        </view>
        <view class="step">
          <text class="step-num">4</text>
          <text class="step-desc">右键点击 getOpenId 文件夹</text>
        </view>
        <view class="step">
          <text class="step-num">5</text>
          <text class="step-desc">选择"创建并部署：云端安装依赖"</text>
        </view>
        <view class="step">
          <text class="step-num">6</text>
          <text class="step-desc">等待部署完成，然后点击"重新测试"</text>
        </view>
      </view>
    </view>
  </view>
</view>
