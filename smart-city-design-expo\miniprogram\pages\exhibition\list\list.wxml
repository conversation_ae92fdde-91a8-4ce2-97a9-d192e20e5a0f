<!--pages/exhibition/list/list.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">🏛️ 设计展览</text>
    <text class="page-subtitle">发现精彩的智慧城市设计展览</text>
  </view>

  <!-- 展览列表 -->
  <view class="exhibitions-list" wx:if="{{exhibitions.length > 0}}">
    <view class="exhibition-card fade-in" 
          wx:for="{{exhibitions}}" 
          wx:key="_id" 
          bindtap="onExhibitionTap" 
          data-id="{{item._id}}">
      
      <!-- 展览封面 -->
      <view class="exhibition-cover">
        <image class="exhibition-image" src="{{item.coverImage}}" mode="aspectFill" />
        <view class="exhibition-status {{getStatusClass(item.status)}}">
          <text class="status-text">{{getStatusText(item.status)}}</text>
        </view>
      </view>

      <!-- 展览信息 -->
      <view class="exhibition-info">
        <view class="exhibition-header">
          <text class="exhibition-title">{{item.title}}</text>
          <view class="exhibition-theme">
            <text class="theme-text">{{item.theme}}</text>
          </view>
        </view>

        <text class="exhibition-description">{{item.description}}</text>

        <!-- 展览标签 -->
        <view class="exhibition-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <text class="tag" wx:for="{{item.tags}}" wx:key="*this">#{{item}}</text>
        </view>

        <!-- 展览详情 -->
        <view class="exhibition-details">
          <view class="detail-item">
            <text class="detail-icon">📍</text>
            <text class="detail-text">{{item.location}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-icon">📅</text>
            <text class="detail-text">{{formatDate(item.startDate)}} - {{formatDate(item.endDate)}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-icon">👥</text>
            <text class="detail-text">{{item.registrationCount}}/{{item.maxRegistration}} 人报名</text>
          </view>
        </view>

        <!-- 展览统计 -->
        <view class="exhibition-stats">
          <view class="stat-item">
            <text class="stat-icon">👁️</text>
            <text class="stat-text">{{item.viewCount}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">👍</text>
            <text class="stat-text">{{item.likeCount}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-icon">🏷️</text>
            <text class="stat-text">{{item.category}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="exhibition-actions">
          <button class="btn btn-secondary btn-small" bindtap="onExhibitionTap" data-id="{{item._id}}">
            查看详情
          </button>
          <button class="btn btn-primary btn-small" 
                  bindtap="onRegisterTap" 
                  data-id="{{item._id}}"
                  wx:if="{{item.status === 'active'}}">
            立即报名
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading && exhibitions.length === 0}}">
    <text>🔄 加载中...</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{loading && exhibitions.length > 0}}">
    <text>🔄 加载更多...</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!hasMore && exhibitions.length > 0}}">
    <text>📄 没有更多展览了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && exhibitions.length === 0}}">
    <view class="empty-icon">🏛️</view>
    <text class="empty-text">暂无展览信息</text>
    <button class="btn btn-primary mt-20" bindtap="refreshData">重新加载</button>
  </view>
</view>
