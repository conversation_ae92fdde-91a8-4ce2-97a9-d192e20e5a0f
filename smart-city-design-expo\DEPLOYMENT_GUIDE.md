# 🚀 智慧城市设计展小程序部署指南

## 📋 部署前准备

### 1. 环境确认
- ✅ 微信开发者工具已安装
- ✅ 云开发环境已创建：`danny-0g3qixz86747c5bb`
- ✅ 小程序 AppID：`wxde0a110374e42908`

### 2. 项目结构确认
```
smart-city-design-expo/
├── miniprogram/           # 小程序前端代码
├── cloudfunctions/        # 云函数代码
├── project.config.json    # 项目配置
└── cloudbaserc.json      # 云开发配置
```

## 🔧 部署步骤

### 步骤 1：导入项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录：选择 `smart-city-design-expo` 文件夹
4. AppID：`wxde0a110374e42908`
5. 项目名称：智慧城市设计展
6. 点击"导入"

### 步骤 2：配置云开发
1. 在开发者工具中点击"云开发"按钮
2. 确认环境选择：`danny-0g3qixz86747c5bb`
3. 等待云开发控制台加载完成

### 步骤 3：部署云函数
按以下顺序部署云函数：

#### 3.1 部署 user-management 云函数
1. 右键 `cloudfunctions/user-management` 文件夹
2. 选择"创建并部署：云端安装依赖"
3. 等待部署完成

#### 3.2 部署 content-management 云函数
1. 右键 `cloudfunctions/content-management` 文件夹
2. 选择"创建并部署：云端安装依赖"
3. 等待部署完成

#### 3.3 部署 registration-system 云函数
1. 右键 `cloudfunctions/registration-system` 文件夹
2. 选择"创建并部署：云端安装依赖"
3. 等待部署完成

### 步骤 4：初始化数据
1. 编译小程序（点击"编译"按钮）
2. 小程序会自动打开"数据初始化"页面
3. 点击"🎯 开始初始化"按钮
4. 等待数据初始化完成
5. 点击"🏠 进入首页"

### 步骤 5：功能测试
测试以下核心功能：
- ✅ 首页轮播图和内容展示
- ✅ 展览列表浏览
- ✅ 报名表单填写
- ✅ 用户信息管理

## 🔍 故障排除

### 问题 1：云函数部署失败
**症状**：云函数创建或部署时报错
**解决方案**：
1. 确认网络连接正常
2. 检查云开发环境是否正确
3. 重新尝试部署
4. 查看云开发控制台的错误日志

### 问题 2：数据初始化失败
**症状**：点击初始化按钮后报错
**解决方案**：
1. 确认 content-management 云函数已部署
2. 检查云开发环境权限
3. 查看云函数调用日志
4. 重新尝试初始化

### 问题 3：页面显示空白
**症状**：首页或其他页面显示空白
**解决方案**：
1. 确认数据已初始化
2. 检查云函数调用是否正常
3. 查看控制台错误信息
4. 重新编译小程序

### 问题 4：报名功能异常
**症状**：报名提交失败
**解决方案**：
1. 确认 registration-system 云函数已部署
2. 检查用户信息是否完整
3. 查看云函数执行日志
4. 验证表单数据格式

## 📊 数据库权限配置

如果遇到数据库权限问题，请在云开发控制台中配置以下权限：

### users 集合
```json
{
  "read": "auth.openid == resource.openid",
  "write": "auth.openid == resource.openid"
}
```

### exhibitions 集合
```json
{
  "read": true,
  "write": false
}
```

### works 集合
```json
{
  "read": "resource.status == 'published'",
  "write": "auth.openid == resource.authorId"
}
```

### registrations 集合
```json
{
  "read": "auth.openid == resource.userId",
  "write": "auth.openid == resource.userId"
}
```

## 🎯 部署完成检查清单

- [ ] 项目成功导入微信开发者工具
- [ ] 云开发环境连接正常
- [ ] 所有云函数部署成功
- [ ] 数据初始化完成
- [ ] 首页内容正常显示
- [ ] 展览列表可以浏览
- [ ] 报名功能正常工作
- [ ] 用户信息管理正常

## 📞 技术支持

如果在部署过程中遇到问题，请：
1. 查看微信开发者工具控制台错误信息
2. 检查云开发控制台的云函数日志
3. 确认网络连接和权限配置
4. 参考微信小程序云开发官方文档

---

🎉 **部署完成后，您就可以开始体验智慧城市设计展小程序的完整功能了！**
